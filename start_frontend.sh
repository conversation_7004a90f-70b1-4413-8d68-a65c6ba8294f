#!/bin/bash

echo "========================================"
echo "启动密码管理系统前端服务"
echo "========================================"

# 切换到前端目录
cd "$(dirname "$0")/frontend"

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装，请先安装 Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "Node.js 版本: $(node --version)"

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "安装依赖..."
    npm install
fi

echo "启动前端开发服务器..."
echo "服务地址: http://localhost:5668"
echo "按 Ctrl+C 停止服务"
echo "========================================"

npm run dev:antd
