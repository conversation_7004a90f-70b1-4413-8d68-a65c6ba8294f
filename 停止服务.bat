@echo off
echo ========================================
echo 停止密码管理系统服务
echo ========================================

echo 正在停止后端服务...
taskkill /f /im python.exe 2>nul
if %errorlevel% == 0 (
    echo 后端服务已停止
) else (
    echo 未找到运行中的后端服务
)

echo 正在停止前端服务...
taskkill /f /im node.exe 2>nul
if %errorlevel% == 0 (
    echo 前端服务已停止
) else (
    echo 未找到运行中的前端服务
)

echo.
echo ========================================
echo 所有服务已停止
echo ========================================
pause
