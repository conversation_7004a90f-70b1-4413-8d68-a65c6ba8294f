from rest_framework import serializers
from django.utils.translation import gettext_lazy as _
from .models import (
    PasswordEntry,
    Category,
    CustomField,
    Attachment,
    PasswordHistory,
    PasswordEntryGroup,
    GroupPermission,
    PasswordEntryGroupMembership,
    PasswordPolicy,
)
from utils.encryption import encrypt_data, decrypt_data
import secrets
import string
import re


class CategorySerializer(serializers.ModelSerializer):
    """分类序列化器"""

    password_count = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            "id",
            "name",
            "description",
            "icon",
            "color",
            "user",
            "password_count",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "user", "created_at", "updated_at"]

    def get_password_count(self, obj):
        """获取分类下的密码数量"""
        return obj.passwordentry_set.count()

    def create(self, validated_data):
        validated_data["user"] = self.context["request"].user
        return super().create(validated_data)


class CustomFieldSerializer(serializers.ModelSerializer):
    """自定义字段序列化器"""

    class Meta:
        model = CustomField
        fields = [
            "id",
            "field_name",
            "field_type",
            "field_value",
            "is_sensitive",
            "order",
            "password_entry",
        ]
        read_only_fields = ["id"]

    def to_representation(self, instance):
        """序列化时解密加密字段"""
        data = super().to_representation(instance)
        if instance.is_sensitive and instance.field_value:
            try:
                data["field_value"] = decrypt_data(instance.field_value)
            except Exception:
                data["field_value"] = "***解密失败***"
        return data

    def create(self, validated_data):
        """创建时加密敏感字段"""
        if validated_data.get("is_sensitive") and validated_data.get("field_value"):
            validated_data["field_value"] = encrypt_data(validated_data["field_value"])
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """更新时加密敏感字段"""
        if validated_data.get("is_sensitive") and validated_data.get("field_value"):
            validated_data["field_value"] = encrypt_data(validated_data["field_value"])
        return super().update(instance, validated_data)


class AttachmentSerializer(serializers.ModelSerializer):
    """附件序列化器"""

    file_size_display = serializers.SerializerMethodField()
    uploaded_by_name = serializers.CharField(
        source="uploaded_by.get_full_name", read_only=True
    )

    class Meta:
        model = Attachment
        fields = [
            "id",
            "file_name",
            "file_path",
            "file_size",
            "file_size_display",
            "content_type",
            "password_entry",
            "uploaded_by",
            "uploaded_by_name",
            "uploaded_at",
        ]
        read_only_fields = ["id", "file_size", "uploaded_at"]

    def get_file_size_display(self, obj):
        """格式化文件大小显示"""
        if obj.file_size < 1024:
            return f"{obj.file_size} B"
        elif obj.file_size < 1024 * 1024:
            return f"{obj.file_size / 1024:.1f} KB"
        else:
            return f"{obj.file_size / (1024 * 1024):.1f} MB"


class PasswordHistorySerializer(serializers.ModelSerializer):
    """密码历史序列化器"""

    class Meta:
        model = PasswordHistory
        fields = ["id", "old_password", "changed_at", "changed_by"]
        read_only_fields = ["id", "changed_at"]


class PasswordEntrySerializer(serializers.ModelSerializer):
    """密码条目序列化器"""

    category_name = serializers.CharField(source="category.name", read_only=True)
    custom_fields = CustomFieldSerializer(many=True, read_only=True)
    attachments = AttachmentSerializer(many=True, read_only=True)
    password_strength = serializers.SerializerMethodField()

    # 组相关字段
    groups_data = serializers.SerializerMethodField()
    user_groups_permissions = serializers.SerializerMethodField()
    group_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="密码组ID列表",
    )

    # 添加便捷字段
    connection_string = serializers.ReadOnlyField()
    full_url = serializers.ReadOnlyField()
    database_connection_string = serializers.ReadOnlyField()
    ssh_command = serializers.ReadOnlyField()
    system_info = serializers.ReadOnlyField()

    # 显示字段
    system_type_display = serializers.CharField(
        source="get_system_type_display", read_only=True
    )
    os_type_display = serializers.CharField(
        source="get_os_type_display", read_only=True
    )
    mdw_type_display = serializers.CharField(
        source="get_mdw_type_display", read_only=True
    )
    database_type_display = serializers.CharField(
        source="get_database_type_display", read_only=True
    )
    environment_display = serializers.CharField(
        source="get_environment_display", read_only=True
    )

    # 软删除相关字段
    deleted_by_username = serializers.CharField(
        source="deleted_by.username", read_only=True
    )

    class Meta:
        model = PasswordEntry
        fields = [
            "id",
            "title",
            # 基本认证信息
            "username",
            "password",
            # 系统连接信息
            "system_type",
            "system_type_display",
            "ip_address",
            "port",
            "url",
            # 系统类型特定信息
            "os_type",
            "os_type_display",
            "mdw_type",
            "mdw_type_display",
            "database_type",
            "database_type_display",
            "database_name",
            # 环境和业务信息
            "environment",
            "environment_display",
            "project_name",
            "created_by",
            # 备注
            "notes",
            # 分类和组
            "category",
            "category_name",
            "groups",
            "groups_data",
            "user_groups_permissions",
            "group_ids",
            # 自定义字段和附件
            "custom_fields",
            "attachments",
            # 安全和状态信息
            "strength",
            "is_favorite",
            "is_pinned",
            "expires_at",
            "last_used",
            "password_strength",
            # 便捷字段
            "connection_string",
            "full_url",
            "database_connection_string",
            "ssh_command",
            "system_info",
            # 系统字段
            "owner",
            "created_at",
            "updated_at",
            # 软删除字段
            "is_deleted",
            "deleted_at",
            "deleted_by",
            "deleted_by_username",
        ]
        read_only_fields = [
            "id",
            "owner",
            "last_used",
            "created_at",
            "updated_at",
            "connection_string",
            "full_url",
            "database_connection_string",
            "ssh_command",
            "system_info",
            "system_type_display",
            "database_type_display",
            "protocol_display",
            "environment_display",
            "is_deleted",
            "deleted_at",
            "deleted_by",
            "deleted_by_username",
        ]
        extra_kwargs = {"password": {"write_only": True}}

    def get_password_strength(self, obj):
        """计算密码强度"""
        if not obj.password:
            return 0

        try:
            # 解密密码进行强度计算
            password = decrypt_data(obj.password)
            return self.calculate_password_strength(password)
        except Exception:
            return 0

    def calculate_password_strength(self, password):
        """计算密码强度（0-100）"""
        if not password:
            return 0

        score = 0

        # 长度评分
        length = len(password)
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
        elif length >= 6:
            score += 10

        # 字符类型评分
        if re.search(r"[a-z]", password):
            score += 15
        if re.search(r"[A-Z]", password):
            score += 15
        if re.search(r"\d", password):
            score += 15
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 20

        # 复杂度评分
        if len(set(password)) > len(password) * 0.7:  # 字符多样性
            score += 10

        return min(score, 100)

    def get_groups_data(self, obj):
        """获取密码条目所属的组信息"""
        groups = obj.groups.filter(is_active=True)
        return [
            {
                "id": group.id,
                "name": group.name,
                "description": group.description,
            }
            for group in groups
        ]

    def get_user_groups_permissions(self, obj):
        """获取当前用户对该密码条目所属组的权限"""
        request = self.context.get("request")
        if not request or not request.user.is_authenticated:
            return []

        user_permissions = []
        for group in obj.groups.filter(is_active=True):
            try:
                permission = GroupPermission.objects.get(user=request.user, group=group)
                user_permissions.append(
                    {
                        "group_id": group.id,
                        "group_name": group.name,
                        "permission": permission.permission,
                        "permission_display": permission.get_permission_display(),
                    }
                )
            except GroupPermission.DoesNotExist:
                continue

        return user_permissions

    def to_representation(self, instance):
        """序列化时解密密码"""
        data = super().to_representation(instance)
        # 在列表视图中不返回密码，只在详情视图中返回
        if self.context.get("request") and self.context["request"].method == "GET":
            if "password" in data:
                del data["password"]
        return data

    def create(self, validated_data):
        """创建密码条目时加密密码"""
        # 加密密码
        if "password" in validated_data and validated_data["password"]:
            validated_data["password"] = encrypt_data(validated_data["password"])

        validated_data["owner"] = self.context["request"].user
        password_entry = super().create(validated_data)

        # 设置标志表示密码已加密，避免模型save方法重复加密
        password_entry._password_encrypted = True

        return password_entry

    def update(self, instance, validated_data):
        """更新密码条目时处理密码加密和历史记录"""
        # 提取密码组ID
        group_ids = validated_data.pop("group_ids", None)

        # 如果密码发生变化，保存历史记录
        if "password" in validated_data and validated_data["password"]:
            old_password = instance.password
            new_password = validated_data["password"]

            # 加密新密码
            validated_data["password"] = encrypt_data(new_password)

            # 保存密码历史
            if old_password != validated_data["password"]:
                PasswordHistory.objects.create(
                    password_entry=instance,
                    old_password=old_password,
                    changed_by=self.context["request"].user,
                )

            # 设置标志表示密码已加密
            instance._password_encrypted = True

        # 更新基本字段
        password_entry = super().update(instance, validated_data)

        # 更新密码组关联
        if group_ids is not None:
            from .models import PasswordEntryGroup, PasswordEntryGroupMembership

            # 删除现有的组关联
            PasswordEntryGroupMembership.objects.filter(
                password_entry=instance
            ).delete()

            # 添加新的组关联
            for group_id in group_ids:
                try:
                    group = PasswordEntryGroup.objects.get(id=group_id, is_active=True)
                    PasswordEntryGroupMembership.objects.create(
                        password_entry=password_entry,
                        group=group,
                        added_by=self.context["request"].user,
                    )
                except PasswordEntryGroup.DoesNotExist:
                    pass  # 忽略不存在的组

        return password_entry


class PasswordEntryDetailSerializer(PasswordEntrySerializer):
    """密码条目详情序列化器（包含密码）"""

    password_history = PasswordHistorySerializer(many=True, read_only=True)

    class Meta(PasswordEntrySerializer.Meta):
        fields = PasswordEntrySerializer.Meta.fields + ["password_history"]

    def to_representation(self, instance):
        """序列化时不返回密码（安全考虑）"""
        data = super(PasswordEntrySerializer, self).to_representation(instance)

        # 为了安全，详情API也不返回密码
        # 密码只能通过专门的复制API获取
        if "password" in data:
            del data["password"]

        return data


class PasswordEntryCreateSerializer(serializers.ModelSerializer):
    """密码条目创建序列化器"""

    category_name = serializers.CharField(source="category.name", read_only=True)
    group_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="密码组ID列表",
    )

    class Meta:
        model = PasswordEntry
        fields = [
            "id",
            "title",
            # 基本认证信息
            "username",
            "password",
            # 系统连接信息
            "system_type",
            "ip_address",
            "port",
            "url",
            # 系统类型特定信息
            "os_type",
            "mdw_type",
            "database_type",
            "database_name",
            # 环境和业务信息
            "environment",
            "project_name",
            "created_by",
            # 备注
            "notes",
            # 分类和密码组
            "category",
            "category_name",
            "group_ids",
            # 状态信息
            "is_favorite",
            "expires_at",
            # 系统字段
            "owner",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "owner",
            "created_at",
            "updated_at",
            "category_name",
        ]

    def create(self, validated_data):
        # 提取密码组ID
        group_ids = validated_data.pop("group_ids", [])

        # 加密密码
        if "password" in validated_data and validated_data["password"]:
            validated_data["password"] = encrypt_data(validated_data["password"])

        validated_data["owner"] = self.context["request"].user
        password_entry = PasswordEntry.objects.create(**validated_data)

        # 设置标志表示密码已加密
        password_entry._password_encrypted = True

        # 添加到密码组
        if group_ids:
            from .models import PasswordEntryGroup, PasswordEntryGroupMembership

            for group_id in group_ids:
                try:
                    group = PasswordEntryGroup.objects.get(id=group_id, is_active=True)
                    PasswordEntryGroupMembership.objects.create(
                        password_entry=password_entry,
                        group=group,
                        added_by=self.context["request"].user,
                    )
                except PasswordEntryGroup.DoesNotExist:
                    pass  # 忽略不存在的组

        return password_entry


class PasswordGeneratorSerializer(serializers.Serializer):
    """密码生成器序列化器"""

    length = serializers.IntegerField(min_value=4, max_value=128, default=12)
    include_uppercase = serializers.BooleanField(default=True)
    include_lowercase = serializers.BooleanField(default=True)
    include_numbers = serializers.BooleanField(default=True)
    include_symbols = serializers.BooleanField(default=True)
    exclude_ambiguous = serializers.BooleanField(default=True)
    custom_symbols = serializers.CharField(
        max_length=50, required=False, allow_blank=True
    )

    def validate(self, attrs):
        """验证至少选择一种字符类型"""
        if not any(
            [
                attrs.get("include_uppercase"),
                attrs.get("include_lowercase"),
                attrs.get("include_numbers"),
                attrs.get("include_symbols"),
            ]
        ):
            raise serializers.ValidationError(_("至少需要选择一种字符类型"))
        return attrs

    def generate_password(self):
        """生成密码"""
        length = self.validated_data["length"]
        include_uppercase = self.validated_data["include_uppercase"]
        include_lowercase = self.validated_data["include_lowercase"]
        include_numbers = self.validated_data["include_numbers"]
        include_symbols = self.validated_data["include_symbols"]
        exclude_ambiguous = self.validated_data["exclude_ambiguous"]
        custom_symbols = self.validated_data.get("custom_symbols", "")

        characters = ""

        if include_lowercase:
            chars = string.ascii_lowercase
            if exclude_ambiguous:
                chars = chars.replace("l", "").replace("o", "")
            characters += chars

        if include_uppercase:
            chars = string.ascii_uppercase
            if exclude_ambiguous:
                chars = chars.replace("I", "").replace("O", "")
            characters += chars

        if include_numbers:
            chars = string.digits
            if exclude_ambiguous:
                chars = chars.replace("0", "").replace("1", "")
            characters += chars

        if include_symbols:
            if custom_symbols:
                characters += custom_symbols
            else:
                chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
                if exclude_ambiguous:
                    chars = chars.replace("|", "").replace("l", "")
                characters += chars

        if not characters:
            raise serializers.ValidationError(_("没有可用的字符生成密码"))

        # 生成密码
        password = "".join(secrets.choice(characters) for _ in range(length))

        return {
            "password": password,
            "strength": self.calculate_password_strength(password),
        }

    def calculate_password_strength(self, password):
        """计算密码强度（0-100）"""
        if not password:
            return 0

        score = 0

        # 长度评分
        length = len(password)
        if length >= 12:
            score += 25
        elif length >= 8:
            score += 15
        elif length >= 6:
            score += 10

        # 字符类型评分
        if re.search(r"[a-z]", password):
            score += 15
        if re.search(r"[A-Z]", password):
            score += 15
        if re.search(r"\d", password):
            score += 15
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 20

        # 复杂度评分
        if len(set(password)) > len(password) * 0.7:  # 字符多样性
            score += 10

        return min(score, 100)


class PasswordSearchSerializer(serializers.Serializer):
    """密码搜索序列化器"""

    query = serializers.CharField(max_length=255, required=False, allow_blank=True)
    category = serializers.IntegerField(required=False)

    is_favorite = serializers.BooleanField(required=False)
    expires_soon = serializers.BooleanField(required=False)  # 即将过期
    weak_passwords = serializers.BooleanField(required=False)  # 弱密码
    duplicate_passwords = serializers.BooleanField(required=False)  # 重复密码
    ordering = serializers.ChoiceField(
        choices=[
            "title",
            "-title",
            "created_at",
            "-created_at",
            "updated_at",
            "-updated_at",
            "last_accessed_at",
            "-last_accessed_at",
        ],
        required=False,
        default="-updated_at",
    )


class PasswordEntryGroupSerializer(serializers.ModelSerializer):
    """密码组序列化器"""

    members_count = serializers.SerializerMethodField()
    password_entries_count = serializers.SerializerMethodField()
    user_permission = serializers.SerializerMethodField()
    created_by_username = serializers.CharField(
        source="created_by.username", read_only=True
    )

    class Meta:
        model = PasswordEntryGroup
        fields = [
            "id",
            "name",
            "description",
            "created_by",
            "created_by_username",
            "created_at",
            "updated_at",
            "is_active",
            "members_count",
            "password_entries_count",
            "user_permission",
        ]
        read_only_fields = ["id", "created_by", "created_at", "updated_at"]

    def get_members_count(self, obj):
        """获取组成员数量"""
        return obj.get_members_count()

    def get_password_entries_count(self, obj):
        """获取组内密码条目数量"""
        return obj.get_password_entries_count()

    def get_user_permission(self, obj):
        """获取当前用户在该组的权限"""
        request = self.context.get("request")
        if request and request.user.is_authenticated:
            try:
                permission = GroupPermission.objects.get(user=request.user, group=obj)
                return permission.permission
            except GroupPermission.DoesNotExist:
                return None
        return None

    def create(self, validated_data):
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)


class PasswordEntryGroupDetailSerializer(PasswordEntryGroupSerializer):
    """密码组详情序列化器 - 包含完整的组信息"""

    password_entries = serializers.SerializerMethodField()
    members = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta(PasswordEntryGroupSerializer.Meta):
        fields = PasswordEntryGroupSerializer.Meta.fields + [
            "password_entries",
            "members",
            "permissions",
        ]

    def get_password_entries(self, obj):
        """获取组内密码条目列表"""
        from utils.password_permissions import password_permission_service

        request = self.context.get("request")
        if not request or not request.user.is_authenticated:
            return []

        # 获取组内所有密码条目
        password_entries = obj.password_entries.filter(is_deleted=False)

        entries_data = []
        for entry in password_entries:
            # 检查用户对该密码的权限
            permission_level = password_permission_service.get_user_permission(
                entry.id, request.user.id
            )

            # 只有有权限的密码才返回
            if permission_level != "none":
                entries_data.append(
                    {
                        "id": entry.id,
                        "title": entry.title,
                        "username": (
                            entry.username
                            if permission_level in ["read", "write", "admin"]
                            else "***"
                        ),
                        "system_type": entry.system_type,
                        "environment": entry.environment,
                        "url": entry.url,
                        "permission_level": permission_level,
                        "created_at": entry.created_at.isoformat(),
                        "updated_at": entry.updated_at.isoformat(),
                    }
                )

        return entries_data

    def get_members(self, obj):
        """获取组成员列表"""
        request = self.context.get("request")
        if not request or not request.user.is_authenticated:
            return []

        # 检查当前用户是否有查看成员的权限
        user_permission = self.get_user_permission(obj)
        if not user_permission or user_permission not in ["manage", "admin"]:
            # 如果不是管理员，只能看到自己
            if request.user == obj.created_by:
                pass  # 创建者可以看到所有成员
            else:
                return []

        members = GroupPermission.objects.filter(group=obj).select_related(
            "user", "granted_by"
        )
        members_data = []

        for member in members:
            members_data.append(
                {
                    "id": member.id,
                    "user_id": member.user.id,
                    "username": member.user.username,
                    "email": member.user.email,
                    "permission": member.permission,
                    "permission_display": member.get_permission_display(),
                    "granted_by": {
                        "id": member.granted_by.id,
                        "username": member.granted_by.username,
                    },
                    "granted_at": member.granted_at.isoformat(),
                }
            )

        return members_data

    def get_permissions(self, obj):
        """获取组的权限设置"""
        request = self.context.get("request")
        if not request or not request.user.is_authenticated:
            return {}

        # 检查当前用户是否有查看权限设置的权限
        user_permission = self.get_user_permission(obj)
        if not user_permission or user_permission not in ["manage", "admin"]:
            if request.user != obj.created_by:
                return {}

        return {
            "created_by": {
                "id": obj.created_by.id,
                "username": obj.created_by.username,
                "email": obj.created_by.email,
            },
            "permission_levels": [
                {"value": "view", "label": "查看"},
                {"value": "edit", "label": "编辑"},
                {"value": "manage", "label": "管理"},
                {"value": "admin", "label": "管理员"},
            ],
        }


class GroupPermissionSerializer(serializers.ModelSerializer):
    """组权限序列化器"""

    username = serializers.CharField(source="user.username", read_only=True)
    user_email = serializers.CharField(source="user.email", read_only=True)
    group_name = serializers.CharField(source="group.name", read_only=True)
    granted_by_username = serializers.CharField(
        source="granted_by.username", read_only=True
    )
    permission_display = serializers.CharField(
        source="get_permission_display", read_only=True
    )

    class Meta:
        model = GroupPermission
        fields = [
            "id",
            "user",
            "username",
            "user_email",
            "group",
            "group_name",
            "permission",
            "permission_display",
            "granted_by",
            "granted_by_username",
            "granted_at",
        ]
        read_only_fields = ["id", "granted_by", "granted_at"]

    def create(self, validated_data):
        validated_data["granted_by"] = self.context["request"].user
        return super().create(validated_data)


class PasswordEntryGroupMembershipSerializer(serializers.ModelSerializer):
    """密码条目组成员关系序列化器"""

    password_entry_title = serializers.CharField(
        source="password_entry.title", read_only=True
    )
    group_name = serializers.CharField(source="group.name", read_only=True)
    added_by_username = serializers.CharField(
        source="added_by.username", read_only=True
    )

    class Meta:
        model = PasswordEntryGroupMembership
        fields = [
            "id",
            "password_entry",
            "password_entry_title",
            "group",
            "group_name",
            "added_by",
            "added_by_username",
            "added_at",
        ]
        read_only_fields = ["id", "added_by", "added_at"]

    def create(self, validated_data):
        validated_data["added_by"] = self.context["request"].user
        return super().create(validated_data)


class PasswordPolicySerializer(serializers.ModelSerializer):
    """密码策略序列化器"""

    created_by_username = serializers.CharField(
        source="created_by.username", read_only=True
    )

    class Meta:
        model = PasswordPolicy
        fields = [
            "id",
            "name",
            "description",
            "min_length",
            "max_length",
            "uppercase_count",
            "lowercase_count",
            "digit_count",
            "special_char_count",
            "allowed_special_chars",
            "allow_repeated_chars",
            "max_repeated_chars",
            "forbid_common_passwords",
            "forbid_personal_info",
            "is_active",
            "is_default",
            "created_by",
            "created_by_username",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_by", "created_at", "updated_at"]

    def create(self, validated_data):
        validated_data["created_by"] = self.context["request"].user
        return super().create(validated_data)

    def validate(self, data):
        """验证密码策略数据"""
        # 验证长度设置
        if data.get("min_length", 0) > data.get("max_length", 128):
            raise serializers.ValidationError("最小长度不能大于最大长度")

        # 验证字符要求总数不超过最小长度
        min_length = data.get("min_length", 8)
        required_chars = (
            data.get("uppercase_count", 0)
            + data.get("lowercase_count", 0)
            + data.get("digit_count", 0)
            + data.get("special_char_count", 0)
        )

        if required_chars > min_length:
            raise serializers.ValidationError(
                f"各类字符要求总数({required_chars})不能超过最小长度({min_length})"
            )

        return data


class PasswordGenerateSerializer(serializers.Serializer):
    """密码生成序列化器"""

    policy_id = serializers.IntegerField(
        required=False, help_text="密码策略ID，如果不提供则使用默认策略"
    )

    # 自定义参数（可选，会覆盖策略设置）
    length = serializers.IntegerField(
        required=False, min_value=4, max_value=128, help_text="密码长度"
    )
    include_uppercase = serializers.BooleanField(
        required=False, help_text="是否包含大写字母"
    )
    include_lowercase = serializers.BooleanField(
        required=False, help_text="是否包含小写字母"
    )
    include_digits = serializers.BooleanField(required=False, help_text="是否包含数字")
    include_symbols = serializers.BooleanField(
        required=False, help_text="是否包含特殊字符"
    )
    custom_symbols = serializers.CharField(
        required=False, max_length=100, help_text="自定义特殊字符集"
    )


class PasswordValidateSerializer(serializers.Serializer):
    """密码验证序列化器"""

    password = serializers.CharField(required=True, help_text="要验证的密码")
    policy_id = serializers.IntegerField(
        required=False, help_text="密码策略ID，如果不提供则使用默认策略"
    )
    username = serializers.CharField(
        required=False, help_text="用户名（用于个人信息检查）"
    )
    email = serializers.EmailField(required=False, help_text="邮箱（用于个人信息检查）")


class PermissionAwarePasswordEntrySerializer(PasswordEntrySerializer):
    """支持权限控制的密码条目序列化器"""

    # 权限相关字段
    user_permission_level = serializers.SerializerMethodField()
    can_browse = serializers.SerializerMethodField()
    can_read = serializers.SerializerMethodField()
    can_write = serializers.SerializerMethodField()
    can_admin = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()

    class Meta(PasswordEntrySerializer.Meta):
        fields = PasswordEntrySerializer.Meta.fields + [
            "user_permission_level",
            "can_browse",
            "can_read",
            "can_write",
            "can_admin",
            "is_owner",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = (
            self.context.get("request").user if self.context.get("request") else None
        )

        # 根据权限动态调整字段
        if self.instance and self.user:
            self._adjust_fields_by_permission()

    def _adjust_fields_by_permission(self):
        """根据用户权限动态调整可见字段"""
        from utils.password_permissions import password_permission_service

        if not self.instance or not self.user:
            return

        # 获取用户权限级别
        permission_level = password_permission_service.get_user_permission(
            self.instance.id, self.user.id
        )

        # 根据权限级别隐藏敏感字段
        if permission_level == "none":
            # 无权限，隐藏所有敏感信息
            sensitive_fields = [
                "username",
                "password",
                "notes",
                "custom_fields",
                "attachments",
            ]
            for field in sensitive_fields:
                if field in self.fields:
                    del self.fields[field]

        elif permission_level == "browse":
            # 浏览权限，隐藏认证信息
            auth_fields = ["username", "password"]
            for field in auth_fields:
                if field in self.fields:
                    del self.fields[field]

    def get_user_permission_level(self, obj):
        """获取用户权限级别"""
        if not self.user:
            return "none"
        from utils.password_permissions import password_permission_service

        return password_permission_service.get_user_permission(obj.id, self.user.id)

    def get_can_browse(self, obj):
        """检查是否有浏览权限"""
        if not self.user:
            return False
        from utils.password_permissions import password_permission_service

        return password_permission_service.can_browse(obj.id, self.user.id)

    def get_can_read(self, obj):
        """检查是否有读取权限"""
        if not self.user:
            return False
        from utils.password_permissions import password_permission_service

        return password_permission_service.can_read(obj.id, self.user.id)

    def get_can_write(self, obj):
        """检查是否有编辑权限"""
        if not self.user:
            return False
        from utils.password_permissions import password_permission_service

        return password_permission_service.can_write(obj.id, self.user.id)

    def get_can_admin(self, obj):
        """检查是否有管理权限"""
        if not self.user:
            return False
        from utils.password_permissions import password_permission_service

        return password_permission_service.can_admin(obj.id, self.user.id)

    def get_is_owner(self, obj):
        """检查是否为owner"""
        if not self.user:
            return False
        from utils.password_permissions import password_permission_service

        return password_permission_service.is_owner(obj.id, self.user.id)

    def to_representation(self, instance):
        """自定义序列化输出，根据权限控制字段显示"""
        data = super().to_representation(instance)

        if not self.user:
            return data

        from utils.password_permissions import password_permission_service

        permission_level = password_permission_service.get_user_permission(
            instance.id, self.user.id
        )

        # 根据权限级别处理敏感字段
        if permission_level == "none":
            # 无权限时，返回权限提示
            data["username"] = "您没有权限查看用户名"
            data["password"] = "您没有权限查看密码"
            data["notes"] = "您没有权限查看备注"

        elif permission_level == "browse":
            # 浏览权限时，隐藏认证信息
            data["username"] = "您没有权限查看用户名"
            data["password"] = "您没有权限查看密码"

        # read、write、admin权限可以查看所有信息，无需特殊处理

        return data
