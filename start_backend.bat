@echo off
chcp 65001 >nul
echo ========================================
echo 启动密码管理系统后端服务
echo ========================================

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%backend"

echo 当前目录: %CD%
echo 检查虚拟环境...
if not exist "venv\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在，请先创建虚拟环境
    echo 运行: python -m venv venv
    pause
    exit /b 1
) else (
    echo 虚拟环境已找到
)

echo 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo 错误: 虚拟环境激活失败
    pause
    exit /b 1
)
echo 虚拟环境激活成功

echo 检查依赖...
pip list | findstr Django >nul
if errorlevel 1 (
    echo 安装依赖...
    pip install -r requirements.txt
)

echo 应用数据库迁移...
python manage.py migrate

echo 启动后端服务...
echo 服务地址: http://localhost:8001
echo API文档: http://localhost:8001/api/docs/
echo 按 Ctrl+C 停止服务
echo ========================================

python manage.py runserver 0.0.0.0:8001
