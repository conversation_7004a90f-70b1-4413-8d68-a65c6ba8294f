# 密码管理系统启动说明

## 🚀 快速启动

### Windows 环境

#### 方式1: 一键启动（最简单）
```bash
# 双击运行，自动启动前端和后端
一键启动.bat
```

#### 方式2: 分别启动
1. **启动后端服务**
   ```bash
   # 双击运行
   启动后端.bat
   # 或手动启动
   cd backend
   .\venv\Scripts\activate
   python .\manage.py runserver 0.0.0.0:8001
   ```

2. **启动前端服务**
   ```bash
   # 双击运行
   启动前端.bat
   # 或手动启动
   cd frontend
   npm run dev:antd
   ```

#### 停止服务
```bash
# 双击运行，停止所有服务
停止服务.bat
```

### Linux/Mac 环境

1. **启动后端服务**
   ```bash
   ./start_backend.sh
   ```

2. **启动前端服务**
   ```bash
   ./start_frontend.sh
   ```

## 📋 服务地址

- **前端应用**: http://localhost:5668
- **后端API**: http://localhost:8001
- **API文档**: http://localhost:8001/api/docs/
- **管理后台**: http://localhost:8001/admin/

## 🔐 默认账户

- **用户名**: admin
- **密码**: admin123

## ⚙️ 环境要求

### 后端要求
- Python 3.8+
- Django 4.2+
- 虚拟环境 (venv)

### 前端要求
- Node.js 16+
- npm 或 yarn

## 🛠️ 手动启动步骤

### 后端手动启动
```bash
cd backend
# Windows
.\venv\Scripts\activate
# Linux/Mac  
source venv/bin/activate

python manage.py migrate
python manage.py runserver 0.0.0.0:8001
```

### 前端手动启动
```bash
cd frontend
npm install
npm run dev:antd
```

## 🔧 故障排除

### 后端启动失败
1. 检查Python版本: `python --version`
2. 检查虚拟环境是否存在
3. 重新安装依赖: `pip install -r requirements.txt`
4. 检查数据库迁移: `python manage.py migrate`

### 前端启动失败
1. 检查Node.js版本: `node --version`
2. 清理依赖重新安装: `rm -rf node_modules && npm install`
3. 检查端口是否被占用

### 权限问题
- Linux/Mac环境下如果脚本无法执行，运行: `chmod +x *.sh`

## 📊 权限系统说明

系统采用基于角色的权限控制(RBAC)，支持以下权限级别：

- **Browse**: 浏览权限 - 可查看密码条目列表，但无法查看密码内容
- **Read**: 读取权限 - 可查看密码条目和密码内容
- **Write**: 编辑权限 - 可编辑密码条目
- **Admin**: 管理权限 - 完全控制，包括权限管理和删除

权限支持用户直接授权和用户组授权两种方式。
