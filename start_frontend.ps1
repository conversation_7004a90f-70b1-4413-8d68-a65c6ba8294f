# 密码管理系统前端启动脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Green
Write-Host "启动密码管理系统前端服务" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 切换到前端目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$frontendPath = Join-Path $scriptPath "frontend"

if (-not (Test-Path $frontendPath)) {
    Write-Host "错误: frontend目录不存在" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Set-Location $frontendPath
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Yellow

# 检查Node.js环境
try {
    $nodeVersion = & node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "错误: Node.js 未安装，请先安装 Node.js" -ForegroundColor Red
    Write-Host "下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查依赖
$nodeModulesPath = Join-Path $frontendPath "node_modules"
if (-not (Test-Path $nodeModulesPath)) {
    Write-Host "安装依赖..." -ForegroundColor Yellow
    & npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

Write-Host "启动前端开发服务器..." -ForegroundColor Green
Write-Host "服务地址: http://localhost:5668" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

& npm run dev:antd
