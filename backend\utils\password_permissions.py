"""
密码权限管理服务
提供密码权限检查、授予、撤销等核心功能
"""

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils import timezone
from django.db import transaction
from typing import Optional, List, Dict, Any
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class PasswordPermissionService:
    """密码权限管理服务类"""

    # 权限级别映射
    PERMISSION_LEVELS = {
        "browse": 1,  # 浏览权限 - 只能看到基本信息，看不到密码
        "read": 2,  # 读取权限 - 可以查看所有信息包括密码
        "write": 3,  # 编辑权限 - 可以修改密码条目信息
        "admin": 4,  # 管理权限 - 完全控制权（owner专有）
    }

    def __init__(self):
        # 延迟导入避免循环依赖
        from apps.passwords.models import (
            PasswordEntry,
            PasswordPermission,
            PasswordEntryGroup,
            UserGroupMembership,
        )

        self.PasswordEntry = PasswordEntry
        self.PasswordPermission = PasswordPermission
        self.PasswordEntryGroup = PasswordEntryGroup
        self.UserGroupMembership = UserGroupMembership

    def get_user_permission(self, password_id: int, user_id: int) -> str:
        """
        获取用户对密码的最高权限级别

        Args:
            password_id: 密码条目ID
            user_id: 用户ID

        Returns:
            权限级别字符串: 'admin', 'write', 'read', 'browse', 'none'
        """
        try:
            # 1. 检查是否为owner（管理员权限）
            if self.is_owner(password_id, user_id):
                return "admin"

            # 2. 检查直接用户权限
            user_permission = self.get_direct_user_permission(password_id, user_id)

            # 3. 检查用户组权限
            group_permission = self.get_group_permission(password_id, user_id)

            # 4. 返回最高权限级别
            return self.get_highest_permission(user_permission, group_permission)

        except Exception as e:
            logger.error(
                f"获取用户权限失败: password_id={password_id}, user_id={user_id}, error={e}"
            )
            return "none"

    def is_owner(self, password_id: int, user_id: int) -> bool:
        """检查用户是否为密码条目的owner"""
        try:
            password = self.PasswordEntry.objects.get(id=password_id)
            return password.owner.id == user_id
        except self.PasswordEntry.DoesNotExist:
            return False

    def get_direct_user_permission(self, password_id: int, user_id: int) -> str:
        """获取用户的直接权限"""
        try:
            permission = self.PasswordPermission.objects.filter(
                password_id=password_id,
                permission_type="user",
                target_id=user_id,
                is_active=True,
            ).first()

            if permission and not permission.is_expired:
                return permission.permission_level
            return "none"
        except Exception as e:
            logger.error(f"获取直接用户权限失败: {e}")
            return "none"

    def get_group_permission(self, password_id: int, user_id: int) -> str:
        """获取用户通过用户组获得的权限"""
        try:
            # 获取用户所属的所有活跃用户组
            user_groups = self.UserGroupMembership.objects.filter(
                user_id=user_id, is_active=True
            ).values_list("group_id", flat=True)

            if not user_groups:
                return "none"

            # 查询这些用户组对密码的权限
            group_permissions = self.PasswordPermission.objects.filter(
                password_id=password_id,
                permission_type="group",
                target_id__in=user_groups,
                is_active=True,
            )

            # 找到最高权限级别
            highest_permission = "none"
            for permission in group_permissions:
                if not permission.is_expired:
                    current_level = self.PERMISSION_LEVELS.get(
                        permission.permission_level, 0
                    )
                    highest_level = self.PERMISSION_LEVELS.get(highest_permission, 0)
                    if current_level > highest_level:
                        highest_permission = permission.permission_level

            return highest_permission
        except Exception as e:
            logger.error(f"获取用户组权限失败: {e}")
            return "none"

    def get_highest_permission(self, permission1: str, permission2: str) -> str:
        """比较两个权限级别，返回更高的权限"""
        level1 = self.PERMISSION_LEVELS.get(permission1, 0)
        level2 = self.PERMISSION_LEVELS.get(permission2, 0)

        if level1 >= level2:
            return permission1 if level1 > 0 else "none"
        else:
            return permission2 if level2 > 0 else "none"

    def has_permission(
        self, password_id: int, user_id: int, required_permission: str
    ) -> bool:
        """
        检查用户是否具有指定权限

        Args:
            password_id: 密码条目ID
            user_id: 用户ID
            required_permission: 需要的权限级别

        Returns:
            bool: 是否具有权限
        """
        user_permission = self.get_user_permission(password_id, user_id)
        user_level = self.PERMISSION_LEVELS.get(user_permission, 0)
        required_level = self.PERMISSION_LEVELS.get(required_permission, 0)
        return user_level >= required_level

    # 便捷的权限检查方法
    def can_browse(self, password_id: int, user_id: int) -> bool:
        """检查是否有浏览权限"""
        return self.has_permission(password_id, user_id, "browse")

    def can_read(self, password_id: int, user_id: int) -> bool:
        """检查是否有读取权限"""
        return self.has_permission(password_id, user_id, "read")

    def can_write(self, password_id: int, user_id: int) -> bool:
        """检查是否有编辑权限"""
        return self.has_permission(password_id, user_id, "write")

    def can_admin(self, password_id: int, user_id: int) -> bool:
        """检查是否有管理权限"""
        return self.has_permission(password_id, user_id, "admin")

    def get_user_accessible_passwords_queryset(
        self, user_id: int, min_permission: str = "browse"
    ):
        """
        获取用户有权限访问的密码条目查询集

        Args:
            user_id: 用户ID
            min_permission: 最低权限要求 ('browse', 'read', 'write', 'admin')

        Returns:
            QuerySet: 过滤后的密码条目查询集
        """
        from django.db.models import Q, Case, When, Value, CharField
        from django.db.models import OuterRef, Exists, Subquery

        # 获取用户所在的组
        user_groups = self.UserGroupMembership.objects.filter(
            user_id=user_id, is_active=True
        ).values_list("group_id", flat=True)

        # 构建权限过滤条件
        permission_conditions = Q()

        # 1. 用户是owner的密码（admin权限）
        permission_conditions |= Q(owner=user_id)

        # 2. 直接用户权限
        user_permission_subquery = self.PasswordPermission.objects.filter(
            password_id=OuterRef("id"),
            permission_type="user",
            target_id=user_id,
            is_active=True,
        ).exclude(expires_at__lt=timezone.now())

        # 3. 用户组权限
        group_permission_subquery = self.PasswordPermission.objects.filter(
            password_id=OuterRef("id"),
            permission_type="group",
            target_id__in=user_groups,
            is_active=True,
        ).exclude(expires_at__lt=timezone.now())

        # 如果需要特定权限级别，添加权限级别过滤
        if min_permission != "browse":
            min_level = self.PERMISSION_LEVELS.get(min_permission, 1)

            # 为权限级别添加数值映射
            permission_level_case = Case(
                When(permission_level="browse", then=Value(1)),
                When(permission_level="read", then=Value(2)),
                When(permission_level="write", then=Value(3)),
                When(permission_level="admin", then=Value(4)),
                default=Value(0),
                output_field=CharField(),
            )

            user_permission_subquery = user_permission_subquery.annotate(
                level_value=permission_level_case
            ).filter(level_value__gte=min_level)

            group_permission_subquery = group_permission_subquery.annotate(
                level_value=permission_level_case
            ).filter(level_value__gte=min_level)

        # 组合权限条件
        permission_conditions |= Q(
            Exists(user_permission_subquery) | Exists(group_permission_subquery)
        )

        # 返回过滤后的查询集
        return self.PasswordEntry.objects.filter(
            permission_conditions, is_deleted=False
        ).distinct()

    def get_user_accessible_passwords_with_permissions(self, user_id: int):
        """
        获取用户有权限访问的密码条目及其权限信息

        Args:
            user_id: 用户ID

        Returns:
            QuerySet: 包含权限信息的密码条目查询集
        """
        from django.db.models import (
            Q,
            Case,
            When,
            Value,
            CharField,
            Max,
            Subquery,
            OuterRef,
        )

        # 获取用户所在的组
        user_groups = self.UserGroupMembership.objects.filter(
            user_id=user_id, is_active=True
        ).values_list("group_id", flat=True)

        # 获取用户的最高权限级别子查询
        user_permission_subquery = (
            self.PasswordPermission.objects.filter(
                password_id=OuterRef("id"),
                permission_type="user",
                target_id=user_id,
                is_active=True,
            )
            .exclude(expires_at__lt=timezone.now())
            .annotate(
                level_value=Case(
                    When(permission_level="browse", then=Value(1)),
                    When(permission_level="read", then=Value(2)),
                    When(permission_level="write", then=Value(3)),
                    When(permission_level="admin", then=Value(4)),
                    default=Value(0),
                )
            )
            .aggregate(max_level=Max("level_value"))["max_level"]
        )

        # 获取用户组的最高权限级别子查询
        group_permission_subquery = (
            self.PasswordPermission.objects.filter(
                password_id=OuterRef("id"),
                permission_type="group",
                target_id__in=user_groups,
                is_active=True,
            )
            .exclude(expires_at__lt=timezone.now())
            .annotate(
                level_value=Case(
                    When(permission_level="browse", then=Value(1)),
                    When(permission_level="read", then=Value(2)),
                    When(permission_level="write", then=Value(3)),
                    When(permission_level="admin", then=Value(4)),
                    default=Value(0),
                )
            )
            .aggregate(max_level=Max("level_value"))["max_level"]
        )

        # 构建查询集，添加权限信息
        queryset = (
            self.PasswordEntry.objects.filter(
                Q(owner=user_id)  # 用户是owner
                | Q(
                    permissions__permission_type="user",
                    permissions__target_id=user_id,
                    permissions__is_active=True,
                )  # 直接用户权限
                | Q(
                    permissions__permission_type="group",
                    permissions__target_id__in=user_groups,
                    permissions__is_active=True,
                ),  # 用户组权限
                is_deleted=False,
            )
            .annotate(
                # 添加用户权限级别注解
                user_permission_level=Case(
                    When(owner=user_id, then=Value("admin")),  # owner有admin权限
                    default=Case(
                        When(
                            permissions__permission_type="user",
                            permissions__target_id=user_id,
                            permissions__is_active=True,
                            then="permissions__permission_level",
                        ),
                        When(
                            permissions__permission_type="group",
                            permissions__target_id__in=user_groups,
                            permissions__is_active=True,
                            then="permissions__permission_level",
                        ),
                        default=Value("none"),
                        output_field=CharField(),
                    ),
                    output_field=CharField(),
                )
            )
            .distinct()
        )

        return queryset

    def grant_permission(
        self,
        password_id: int,
        target_type: str,
        target_id: int,
        permission_level: str,
        granted_by_user_id: int,
        expires_at: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        授予权限

        Args:
            password_id: 密码条目ID
            target_type: 目标类型 ('user' 或 'group')
            target_id: 目标ID (用户ID或用户组ID)
            permission_level: 权限级别
            granted_by_user_id: 授权人用户ID
            expires_at: 过期时间 (可选)

        Returns:
            Dict: 操作结果
        """
        try:
            # 检查授权人是否有管理权限
            if not self.can_admin(password_id, granted_by_user_id):
                return {"success": False, "error": "没有权限进行此操作"}

            # 检查权限级别是否有效
            if permission_level not in ["browse", "read", "write"]:
                return {"success": False, "error": "无效的权限级别"}

            # 创建或更新权限
            permission, created = self.PasswordPermission.objects.update_or_create(
                password_id=password_id,
                permission_type=target_type,
                target_id=target_id,
                defaults={
                    "permission_level": permission_level,
                    "granted_by_id": granted_by_user_id,
                    "expires_at": expires_at,
                    "is_active": True,
                },
            )

            action = "创建" if created else "更新"
            return {
                "success": True,
                "message": f"权限{action}成功",
                "permission_id": permission.id,
            }

        except Exception as e:
            logger.error(f"授予权限失败: {e}")
            return {"success": False, "error": f"授予权限失败: {str(e)}"}

    def revoke_permission(
        self,
        password_id: int,
        target_type: str,
        target_id: int,
        revoked_by_user_id: int,
    ) -> Dict[str, Any]:
        """
        撤销权限

        Args:
            password_id: 密码条目ID
            target_type: 目标类型 ('user' 或 'group')
            target_id: 目标ID
            revoked_by_user_id: 撤销人用户ID

        Returns:
            Dict: 操作结果
        """
        try:
            # 检查撤销人是否有管理权限
            if not self.can_admin(password_id, revoked_by_user_id):
                return {"success": False, "error": "没有权限进行此操作"}

            # 删除权限记录
            deleted_count, _ = self.PasswordPermission.objects.filter(
                password_id=password_id,
                permission_type=target_type,
                target_id=target_id,
            ).delete()

            if deleted_count > 0:
                return {"success": True, "message": "权限撤销成功"}
            else:
                return {"success": False, "error": "未找到要撤销的权限"}

        except Exception as e:
            logger.error(f"撤销权限失败: {e}")
            return {"success": False, "error": f"撤销权限失败: {str(e)}"}

    def transfer_ownership(
        self, password_id: int, new_owner_id: int, current_owner_id: int
    ) -> Dict[str, Any]:
        """
        转移密码条目的所有权

        Args:
            password_id: 密码条目ID
            new_owner_id: 新所有者用户ID
            current_owner_id: 当前所有者用户ID

        Returns:
            Dict: 操作结果
        """
        try:
            # 检查当前用户是否为owner
            if not self.is_owner(password_id, current_owner_id):
                return {"success": False, "error": "只有所有者可以转移所有权"}

            # 检查新所有者是否存在
            if not User.objects.filter(id=new_owner_id).exists():
                return {"success": False, "error": "新所有者不存在"}

            # 更新所有者
            password = self.PasswordEntry.objects.get(id=password_id)
            new_owner = User.objects.get(id=new_owner_id)
            password.owner = new_owner
            password.save(update_fields=["owner"])

            return {"success": True, "message": "所有权转移成功"}

        except self.PasswordEntry.DoesNotExist:
            return {"success": False, "error": "密码条目不存在"}
        except Exception as e:
            logger.error(f"转移所有权失败: {e}")
            return {"success": False, "error": f"转移所有权失败: {str(e)}"}


# 创建全局实例
password_permission_service = PasswordPermissionService()
