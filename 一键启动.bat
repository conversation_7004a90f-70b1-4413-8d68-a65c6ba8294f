@echo off
echo ========================================
echo 密码管理系统一键启动
echo ========================================
echo.
echo 正在启动后端服务...
start "后端服务" cmd /k "cd backend && .\venv\Scripts\activate && python .\manage.py runserver 0.0.0.0:8001"

echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo 正在启动前端服务...
start "前端服务" cmd /k "cd frontend && npm run dev:antd"

echo.
echo ========================================
echo 启动完成！
echo 后端服务: http://localhost:8001
echo 前端应用: http://localhost:5668
echo API文档: http://localhost:8001/api/docs/
echo ========================================
echo.
echo 按任意键退出...
pause >nul
