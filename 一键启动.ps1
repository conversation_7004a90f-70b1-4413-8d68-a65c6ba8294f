# 密码管理系统一键启动脚本

Write-Host "========================================" -ForegroundColor Green
Write-Host "密码管理系统一键启动" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

Write-Host "正在启动后端服务..." -ForegroundColor Yellow
$backendPath = Join-Path $scriptPath "backend"
Start-Process -FilePath "cmd" -ArgumentList "/k", "cd /d `"$backendPath`" && .\venv\Scripts\activate && python .\manage.py runserver 0.0.0.0:8001" -WindowStyle Normal

Write-Host "等待后端服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host "正在启动前端服务..." -ForegroundColor Yellow
$frontendPath = Join-Path $scriptPath "frontend"
Start-Process -FilePath "cmd" -ArgumentList "/k", "cd /d `"$frontendPath`" && npm run dev:antd" -WindowStyle Normal

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "启动完成！" -ForegroundColor Green
Write-Host "后端服务: http://localhost:8001" -ForegroundColor Cyan
Write-Host "前端应用: http://localhost:5668" -ForegroundColor Cyan
Write-Host "API文档: http://localhost:8001/api/docs/" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Yellow
Read-Host
