# 🚀 密码管理系统启动指南

## 📁 项目结构
```
locker/
├── backend/          # Django后端
├── frontend/         # Vue.js前端
├── 一键启动.bat      # Windows批处理启动脚本
├── 一键启动.ps1      # PowerShell启动脚本
├── 启动后端.bat      # 后端启动脚本
├── 启动前端.bat      # 前端启动脚本
├── 停止服务.bat      # 服务停止脚本
└── README_启动说明.md # 详细启动说明
```

## 🎯 快速启动（推荐方式）

### Windows 用户
1. **双击运行**: `一键启动.bat`
2. **或者在PowerShell中**: `powershell -ExecutionPolicy Bypass -File 一键启动.ps1`

### 手动启动
```bash
# 启动后端（新开一个终端）
cd backend
.\venv\Scripts\activate
python .\manage.py runserver 0.0.0.0:8001

# 启动前端（新开另一个终端）
cd frontend
npm run dev:antd
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost:5668 | 主要用户界面 |
| 后端API | http://localhost:8001 | REST API服务 |
| API文档 | http://localhost:8001/api/docs/ | Swagger文档 |
| 管理后台 | http://localhost:8001/admin/ | Django管理界面 |

## 🔐 默认登录信息

- **用户名**: `admin`
- **密码**: `admin123`

## 🛑 停止服务

- **双击运行**: `停止服务.bat`
- **或者手动**: 在各个终端窗口按 `Ctrl+C`

## 🔧 故障排除

### 常见问题
1. **端口被占用**: 检查8001和5668端口是否被其他程序占用
2. **虚拟环境问题**: 确保backend/venv目录存在且完整
3. **依赖问题**: 重新安装依赖包

### 重新安装依赖
```bash
# 后端依赖
cd backend
pip install -r requirements.txt

# 前端依赖
cd frontend
npm install
```

## 📊 权限系统

系统支持以下权限级别：
- **Browse**: 浏览权限 - 可查看密码列表
- **Read**: 读取权限 - 可查看密码内容
- **Write**: 编辑权限 - 可编辑密码
- **Admin**: 管理权限 - 完全控制

## 🎉 开始使用

1. 运行启动脚本
2. 打开浏览器访问 http://localhost:5668
3. 使用默认账户登录
4. 开始管理你的密码！
