#!/bin/bash

echo "========================================"
echo "启动密码管理系统后端服务"
echo "========================================"

# 切换到后端目录
cd "$(dirname "$0")/backend"

# 检查虚拟环境
if [ ! -f "venv/bin/activate" ]; then
    echo "错误: 虚拟环境不存在，请先创建虚拟环境"
    echo "运行: python -m venv venv"
    exit 1
fi

echo "激活虚拟环境..."
source venv/bin/activate

# 检查依赖
if ! pip list | grep -q Django; then
    echo "安装依赖..."
    pip install -r requirements.txt
fi

echo "应用数据库迁移..."
python manage.py migrate

echo "启动后端服务..."
echo "服务地址: http://localhost:8001"
echo "API文档: http://localhost:8001/api/docs/"
echo "按 Ctrl+C 停止服务"
echo "========================================"

python manage.py runserver 0.0.0.0:8001
