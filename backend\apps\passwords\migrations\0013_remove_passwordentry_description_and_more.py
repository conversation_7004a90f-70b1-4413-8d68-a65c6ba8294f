# Generated by Django 5.2.4 on 2025-08-01 13:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0012_alter_passwordentry_protocol"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="passwordentry",
            name="description",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="hostname",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="responsible_person",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="schema_name",
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="created_by",
            field=models.CharField(blank=True, max_length=100, verbose_name="创建者"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="ip_address",
            field=models.GenericIPAddressField(
                blank=True, null=True, verbose_name="IP地址"
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="passwordentry",
            name="mdw_type",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("bes", "BES"),
                    ("was", "WAS"),
                    ("redis", "Redis"),
                    ("other", "其他"),
                ],
                max_length=20,
                verbose_name="中间件类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="database_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("gaussdb", "GaussDB"),
                    ("mysql", "MySQL"),
                    ("postgresql", "PostgreSQL"),
                    ("oracle", "Oracle"),
                    ("other", "其他"),
                ],
                max_length=20,
                verbose_name="数据库类型",
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="project_name",
            field=models.CharField(max_length=100, verbose_name="应用/项目名称"),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="system_type",
            field=models.CharField(
                choices=[
                    ("os", "操作系统"),
                    ("db", "数据库"),
                    ("mdw", "中间件"),
                    ("ftp", "FTP"),
                    ("sftp", "SFTP"),
                    ("网站", "网站"),
                    ("网络设备", "网络设备"),
                    ("其他", "其他"),
                ],
                default="其他",
                max_length=20,
                verbose_name="系统类型",
            ),
        ),
    ]
