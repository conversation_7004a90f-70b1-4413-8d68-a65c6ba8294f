# Generated by Django 5.2.4 on 2025-08-01 15:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0013_remove_passwordentry_description_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="passwordentry",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="ip_address",
        ),
        migrations.RemoveField(
            model_name="passwordentry",
            name="mdw_type",
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="description",
            field=models.TextField(blank=True, verbose_name="系统描述"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="hostname",
            field=models.CharField(
                blank=True, max_length=255, verbose_name="主机名/IP"
            ),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="responsible_person",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100, verbose_name="负责人"),
        ),
        migrations.AddField(
            model_name="passwordentry",
            name="schema_name",
            field=models.CharField(blank=True, max_length=100, verbose_name="模式名"),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="project_name",
            field=models.CharField(
                blank=True, max_length=100, verbose_name="应用/项目名称"
            ),
        ),
        migrations.AlterField(
            model_name="passwordentry",
            name="system_type",
            field=models.CharField(
                choices=[
                    ("os", "操作系统"),
                    ("database", "数据库"),
                    ("middleware", "中间件"),
                    ("ftp", "FTP"),
                    ("sftp", "SFTP"),
                    ("website", "网站"),
                    ("network", "网络设备"),
                    ("other", "其他"),
                ],
                default="other",
                max_length=20,
                verbose_name="系统类型",
            ),
        ),
    ]
