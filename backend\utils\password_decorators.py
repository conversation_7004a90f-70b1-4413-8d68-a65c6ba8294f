"""
密码权限装饰器
提供API视图的权限检查装饰器
"""

from functools import wraps
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from rest_framework import status
from rest_framework.response import Response
from .password_permissions import password_permission_service
import logging

logger = logging.getLogger(__name__)


def require_password_permission(permission_level: str):
    """
    权限检查装饰器
    
    Args:
        permission_level: 需要的权限级别 ('browse', 'read', 'write', 'admin')
    
    Usage:
        @require_password_permission('read')
        def get_password(request, password_id):
            # 只有具有read权限的用户才能访问
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # 从URL参数或请求参数中获取password_id
            password_id = None
            
            # 尝试从URL参数获取
            if 'password_id' in kwargs:
                password_id = kwargs['password_id']
            elif 'pk' in kwargs:
                password_id = kwargs['pk']
            elif 'id' in kwargs:
                password_id = kwargs['id']
            # 尝试从请求参数获取
            elif hasattr(request, 'data') and 'password_id' in request.data:
                password_id = request.data.get('password_id')
            elif request.method == 'GET' and 'password_id' in request.GET:
                password_id = request.GET.get('password_id')
            
            if not password_id:
                return JsonResponse({
                    'error': '缺少密码ID参数',
                    'code': 'MISSING_PASSWORD_ID'
                }, status=400)
            
            try:
                password_id = int(password_id)
            except (ValueError, TypeError):
                return JsonResponse({
                    'error': '无效的密码ID',
                    'code': 'INVALID_PASSWORD_ID'
                }, status=400)
            
            # 检查用户是否已认证
            if not request.user or not request.user.is_authenticated:
                return JsonResponse({
                    'error': '用户未认证',
                    'code': 'UNAUTHENTICATED'
                }, status=401)
            
            user_id = request.user.id
            
            # 检查权限
            try:
                has_permission = password_permission_service.has_permission(
                    password_id, user_id, permission_level
                )
                
                if not has_permission:
                    logger.warning(
                        f"权限检查失败: user_id={user_id}, password_id={password_id}, "
                        f"required_permission={permission_level}"
                    )
                    return JsonResponse({
                        'error': f'需要 {permission_level} 权限',
                        'code': 'INSUFFICIENT_PERMISSION',
                        'required_permission': permission_level
                    }, status=403)
                
                # 权限检查通过，执行原函数
                return view_func(request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"权限检查异常: {e}")
                return JsonResponse({
                    'error': '权限检查失败',
                    'code': 'PERMISSION_CHECK_ERROR'
                }, status=500)
        
        return wrapper
    return decorator


def require_password_permission_drf(permission_level: str):
    """
    DRF视图的权限检查装饰器
    
    Args:
        permission_level: 需要的权限级别 ('browse', 'read', 'write', 'admin')
    
    Usage:
        @require_password_permission_drf('read')
        def get(self, request, password_id):
            # 只有具有read权限的用户才能访问
            pass
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            # 从URL参数获取password_id
            password_id = None
            
            if 'password_id' in kwargs:
                password_id = kwargs['password_id']
            elif 'pk' in kwargs:
                password_id = kwargs['pk']
            elif 'id' in kwargs:
                password_id = kwargs['id']
            elif hasattr(self, 'get_object'):
                # 对于DRF ViewSet，尝试从get_object获取
                try:
                    obj = self.get_object()
                    password_id = obj.id if hasattr(obj, 'id') else None
                except:
                    pass
            
            if not password_id:
                return Response({
                    'error': '缺少密码ID参数',
                    'code': 'MISSING_PASSWORD_ID'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                password_id = int(password_id)
            except (ValueError, TypeError):
                return Response({
                    'error': '无效的密码ID',
                    'code': 'INVALID_PASSWORD_ID'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 检查用户是否已认证
            if not request.user or not request.user.is_authenticated:
                return Response({
                    'error': '用户未认证',
                    'code': 'UNAUTHENTICATED'
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            user_id = request.user.id
            
            # 检查权限
            try:
                has_permission = password_permission_service.has_permission(
                    password_id, user_id, permission_level
                )
                
                if not has_permission:
                    logger.warning(
                        f"权限检查失败: user_id={user_id}, password_id={password_id}, "
                        f"required_permission={permission_level}"
                    )
                    return Response({
                        'error': f'需要 {permission_level} 权限',
                        'code': 'INSUFFICIENT_PERMISSION',
                        'required_permission': permission_level
                    }, status=status.HTTP_403_FORBIDDEN)
                
                # 权限检查通过，执行原函数
                return view_func(self, request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"权限检查异常: {e}")
                return Response({
                    'error': '权限检查失败',
                    'code': 'PERMISSION_CHECK_ERROR'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return wrapper
    return decorator


def check_password_owner(view_func):
    """
    检查用户是否为密码条目的所有者
    
    Usage:
        @check_password_owner
        def delete_password(request, password_id):
            # 只有所有者才能删除密码
            pass
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # 获取password_id
        password_id = kwargs.get('password_id') or kwargs.get('pk') or kwargs.get('id')
        
        if not password_id:
            return JsonResponse({
                'error': '缺少密码ID参数',
                'code': 'MISSING_PASSWORD_ID'
            }, status=400)
        
        try:
            password_id = int(password_id)
        except (ValueError, TypeError):
            return JsonResponse({
                'error': '无效的密码ID',
                'code': 'INVALID_PASSWORD_ID'
            }, status=400)
        
        # 检查用户是否已认证
        if not request.user or not request.user.is_authenticated:
            return JsonResponse({
                'error': '用户未认证',
                'code': 'UNAUTHENTICATED'
            }, status=401)
        
        user_id = request.user.id
        
        # 检查是否为所有者
        try:
            is_owner = password_permission_service.is_owner(password_id, user_id)
            
            if not is_owner:
                logger.warning(f"非所有者访问: user_id={user_id}, password_id={password_id}")
                return JsonResponse({
                    'error': '只有所有者可以执行此操作',
                    'code': 'NOT_OWNER'
                }, status=403)
            
            # 检查通过，执行原函数
            return view_func(request, *args, **kwargs)
            
        except Exception as e:
            logger.error(f"所有者检查异常: {e}")
            return JsonResponse({
                'error': '所有者检查失败',
                'code': 'OWNER_CHECK_ERROR'
            }, status=500)
    
    return wrapper


def get_user_permission_level(request, password_id):
    """
    获取用户对密码的权限级别
    
    Args:
        request: Django请求对象
        password_id: 密码ID
        
    Returns:
        str: 权限级别 ('admin', 'write', 'read', 'browse', 'none')
    """
    if not request.user or not request.user.is_authenticated:
        return 'none'
    
    try:
        return password_permission_service.get_user_permission(password_id, request.user.id)
    except Exception as e:
        logger.error(f"获取用户权限级别失败: {e}")
        return 'none'
