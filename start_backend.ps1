# 密码管理系统后端启动脚本 (PowerShell)

Write-Host "========================================" -ForegroundColor Green
Write-Host "启动密码管理系统后端服务" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 切换到后端目录
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$backendPath = Join-Path $scriptPath "backend"

if (-not (Test-Path $backendPath)) {
    Write-Host "错误: backend目录不存在" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Set-Location $backendPath
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Yellow

# 检查虚拟环境
$venvPath = Join-Path $backendPath "venv\Scripts\activate.bat"
if (-not (Test-Path $venvPath)) {
    Write-Host "错误: 虚拟环境不存在，请先创建虚拟环境" -ForegroundColor Red
    Write-Host "运行: python -m venv venv" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& "venv\Scripts\activate.bat"

# 检查Django是否安装
try {
    $djangoVersion = & python -c "import django; print(django.get_version())" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Django版本: $djangoVersion" -ForegroundColor Green
    } else {
        Write-Host "安装依赖..." -ForegroundColor Yellow
        & pip install -r requirements.txt
    }
} catch {
    Write-Host "安装依赖..." -ForegroundColor Yellow
    & pip install -r requirements.txt
}

Write-Host "应用数据库迁移..." -ForegroundColor Yellow
& python manage.py migrate

Write-Host "启动后端服务..." -ForegroundColor Green
Write-Host "服务地址: http://localhost:8001" -ForegroundColor Cyan
Write-Host "API文档: http://localhost:8001/api/docs/" -ForegroundColor Cyan
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

& python manage.py runserver 0.0.0.0:8001
