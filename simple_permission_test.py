"""
简化的密码权限测试脚本
验证核心权限控制功能
"""

import asyncio
from playwright.async_api import async_playwright
import time

# 测试配置
FRONTEND_URL = "http://localhost:5668"

# 测试用户账号
TEST_USERS = {
    'admin': {'username': 'admin', 'password': 'admin123'},
    'user1': {'username': 'user1', 'password': 'password123'},
    'user2': {'username': 'user2', 'password': 'password123'},
    'viewer1': {'username': 'viewer1', 'password': 'password123'},
}

async def login_user(page, username, password):
    """登录用户"""
    print(f"🔐 登录用户: {username}")
    
    # 访问登录页面
    await page.goto(FRONTEND_URL)
    await page.wait_for_load_state('networkidle')
    
    try:
        # 填写登录表单
        await page.get_by_placeholder("请输入用户名").fill(username)
        await page.get_by_placeholder("密码").fill(password)
        
        # 点击登录按钮
        await page.get_by_role("button", name="login").click()
        
        # 等待登录成功
        await page.wait_for_url("**/passwords", timeout=15000)
        print(f"✅ 用户 {username} 登录成功")
        return True
        
    except Exception as e:
        print(f"❌ 用户 {username} 登录失败: {e}")
        return False

async def test_password_list_access(page, username):
    """测试密码列表访问权限"""
    print(f"\n📋 测试用户 {username} 的密码列表访问权限")
    
    try:
        # 访问密码列表页面
        await page.goto(f"{FRONTEND_URL}/passwords")
        await page.wait_for_load_state('networkidle')
        
        # 检查页面标题
        title = await page.locator('h1').first.text_content()
        if "密码管理" in title:
            print(f"✅ {username} 可以访问密码列表页面")
            
            # 统计可见的密码条目
            password_rows = await page.locator('tbody tr').count()
            print(f"📊 {username} 可以看到 {password_rows} 个密码条目")
            
            # 检查第一个密码条目的详细信息
            if password_rows > 0:
                first_row = page.locator('tbody tr').first
                
                # 检查认证信息列的内容
                auth_cell = first_row.locator('td').nth(3)  # 认证信息列
                auth_text = await auth_cell.text_content()
                
                if "无权限查看" in auth_text:
                    print(f"⚠️ {username} 对某些密码只有浏览权限（看不到认证信息）")
                elif "需要读取权限" in auth_text:
                    print(f"⚠️ {username} 需要更高权限才能查看密码内容")
                else:
                    print(f"✅ {username} 可以查看认证信息")
            
            return True
        else:
            print(f"❌ {username} 无法访问密码列表页面")
            return False
            
    except Exception as e:
        print(f"❌ {username} 访问密码列表失败: {e}")
        return False

async def test_password_detail_access(page, username, password_title):
    """测试密码详情页面访问权限"""
    print(f"\n🔍 测试用户 {username} 访问 {password_title} 的详情")
    
    try:
        # 访问密码列表页面
        await page.goto(f"{FRONTEND_URL}/passwords")
        await page.wait_for_load_state('networkidle')
        
        # 查找并点击目标密码条目
        password_button = page.get_by_role("button", name=password_title)
        if await password_button.count() > 0:
            await password_button.click()
            await page.wait_for_load_state('networkidle')
            
            # 检查是否成功进入详情页面
            page_title = await page.locator('h1').first.text_content()
            if password_title in page_title or "密码详情" in page_title:
                print(f"✅ {username} 可以访问 {password_title} 的详情页面")
                
                # 检查各种操作按钮的可见性
                buttons_status = {}
                
                # 检查编辑按钮
                edit_btn = page.get_by_role("button", name="编辑")
                buttons_status['编辑'] = await edit_btn.count() > 0
                
                # 检查删除按钮
                delete_btn = page.get_by_role("button", name="删除")
                buttons_status['删除'] = await delete_btn.count() > 0
                
                # 检查权限管理按钮
                permission_btn = page.get_by_role("button", name="权限管理")
                buttons_status['权限管理'] = await permission_btn.count() > 0
                
                # 检查分享按钮
                share_btn = page.get_by_role("button", name="分享")
                buttons_status['分享'] = await share_btn.count() > 0
                
                print(f"🔘 {username} 可用的操作按钮: {[k for k, v in buttons_status.items() if v]}")
                
                # 检查密码字段是否可见
                password_inputs = await page.locator('input[type="password"]').count()
                if password_inputs > 0:
                    print(f"🔒 {username} 可以看到密码字段")
                else:
                    print(f"⚠️ {username} 看不到密码字段")
                
                return True
            else:
                print(f"❌ {username} 无法正确访问 {password_title} 的详情页面")
                return False
        else:
            print(f"❌ {username} 找不到密码条目: {password_title}")
            return False
            
    except Exception as e:
        print(f"❌ {username} 访问密码详情失败: {e}")
        return False

async def run_permission_tests():
    """运行权限测试"""
    print("🚀 开始密码权限管理系统测试")
    print("=" * 60)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=500)
        
        # 测试场景定义
        test_scenarios = [
            {
                'user': 'admin',
                'description': 'Admin用户（所有者）- 应该有完全权限',
                'expected_passwords': ['MySQL生产数据库', '管理后台系统'],
                'test_detail': 'MySQL生产数据库'
            },
            {
                'user': 'user1',
                'description': 'User1用户 - 对MySQL有read权限，Redis的owner',
                'expected_passwords': ['Redis缓存服务器'],
                'test_detail': 'Redis缓存服务器'
            },
            {
                'user': 'user2',
                'description': 'User2用户 - 对MySQL有browse权限，SSH的owner',
                'expected_passwords': ['应用服务器SSH'],
                'test_detail': '应用服务器SSH'
            },
            {
                'user': 'viewer1',
                'description': 'Viewer1用户 - 只有部分browse权限',
                'expected_passwords': [],
                'test_detail': None
            }
        ]
        
        for scenario in test_scenarios:
            user_key = scenario['user']
            user_info = TEST_USERS[user_key]
            
            print(f"\n{'='*20} {scenario['description']} {'='*20}")
            
            # 创建新的页面上下文
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                # 登录用户
                login_success = await login_user(page, user_info['username'], user_info['password'])
                
                if login_success:
                    # 测试密码列表访问
                    await test_password_list_access(page, user_info['username'])
                    
                    # 测试密码详情访问
                    if scenario['test_detail']:
                        await test_password_detail_access(page, user_info['username'], scenario['test_detail'])
                    
                    # 等待一下以便观察
                    await page.wait_for_timeout(2000)
                else:
                    print(f"⚠️ 跳过 {user_info['username']} 的后续测试")
                    
            except Exception as e:
                print(f"❌ 测试用户 {user_info['username']} 时发生错误: {e}")
            
            finally:
                await context.close()
        
        await browser.close()
    
    print("\n" + "=" * 60)
    print("✅ 权限测试完成")

if __name__ == "__main__":
    asyncio.run(run_permission_tests())
