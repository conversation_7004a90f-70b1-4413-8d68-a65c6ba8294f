from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import URLValidator
from django.utils import timezone
import uuid

User = get_user_model()


class PasswordEntryGroup(models.Model):
    """密码组模型 - 用于团队协作和权限控制"""

    name = models.CharField(max_length=100, verbose_name=_("组名称"))
    description = models.TextField(blank=True, verbose_name=_("组描述"))
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="created_password_groups",
        verbose_name=_("创建者"),
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))
    is_active = models.BooleanField(default=True, verbose_name=_("是否激活"))

    class Meta:
        verbose_name = _("密码组")
        verbose_name_plural = _("密码组")
        db_table = "password_entry_groups"
        unique_together = ["name", "created_by"]

    def __str__(self):
        return self.name

    def get_members_count(self):
        """获取组成员数量"""
        return self.grouppermission_set.count()

    def get_password_entries_count(self):
        """获取组内密码条目数量"""
        return self.password_entries.count()

    def has_permission(self, user, permission_level):
        """检查用户是否有指定权限"""
        try:
            group_permission = self.grouppermission_set.get(user=user)
            permission_hierarchy = {"view": 1, "edit": 2, "manage": 3, "admin": 4}
            user_level = permission_hierarchy.get(group_permission.permission, 0)
            required_level = permission_hierarchy.get(permission_level, 0)
            return user_level >= required_level
        except GroupPermission.DoesNotExist:
            return False


class GroupPermission(models.Model):
    """组权限模型 - 定义用户在特定组中的权限"""

    PERMISSION_CHOICES = [
        ("view", _("查看")),
        ("edit", _("编辑")),
        ("manage", _("管理")),
        ("admin", _("管理员")),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("用户"))
    group = models.ForeignKey(
        PasswordEntryGroup, on_delete=models.CASCADE, verbose_name=_("密码组")
    )
    permission = models.CharField(
        max_length=10, choices=PERMISSION_CHOICES, verbose_name=_("权限级别")
    )
    granted_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="granted_permissions",
        verbose_name=_("授权者"),
    )
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name=_("授权时间"))

    class Meta:
        verbose_name = _("组权限")
        verbose_name_plural = _("组权限")
        db_table = "password_group_permissions"
        unique_together = ["user", "group"]

    def __str__(self):
        return f"{self.user.username} - {self.group.name} - {self.get_permission_display()}"


class Category(models.Model):
    """密码分类模型"""

    name = models.CharField(max_length=100, verbose_name=_("分类名称"))
    description = models.TextField(blank=True, verbose_name=_("分类描述"))
    icon = models.CharField(max_length=50, blank=True, verbose_name=_("图标"))
    color = models.CharField(max_length=7, default="#1890ff", verbose_name=_("颜色"))
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("上级分类"),
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("创建用户"))
    is_system = models.BooleanField(default=False, verbose_name=_("系统分类"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("密码分类")
        verbose_name_plural = _("密码分类")
        db_table = "password_categories"
        unique_together = ["name", "user", "parent"]

    def __str__(self):
        return self.name


class PasswordEntry(models.Model):
    """密码条目模型"""

    STRENGTH_CHOICES = [
        ("weak", _("弱")),
        ("medium", _("中等")),
        ("strong", _("强")),
        ("very_strong", _("非常强")),
    ]

    # 系统类型选择
    SYSTEM_TYPE_CHOICES = [
        ("os", _("操作系统")),
        ("database", _("数据库")),
        ("middleware", _("中间件")),
        ("ftp", _("FTP")),
        ("sftp", _("SFTP")),
        # ("cloud", _("云服务")),
        # ("application", _("应用系统")),
        ("website", _("网站")),
        ("network", _("网络设备")),
        ("other", _("其他")),
    ]

    # 数据库类型选择
    OS_TYPE_CHOICES = [
        ("kylin", _("Kylin")),
        ("suse", _("SuSE")),
        ("windows", _("Windows")),
        ("uos", _("UOS")),
        ("other", _("其他")),
    ]

    # 数据库类型选择
    MDW_TYPE_CHOICES = [
        ("bes", _("BES")),
        ("was", _("WAS")),
        ("redis", _("Redis")),
        ("other", _("其他")),
    ]

    # 数据库类型选择
    DATABASE_TYPE_CHOICES = [
        ("gaussdb", _("GaussDB")),
        ("mysql", _("MySQL")),
        ("postgresql", _("PostgreSQL")),
        ("oracle", _("Oracle")),
        ("other", _("其他")),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200, verbose_name=_("标题"))
    # 基本认证信息
    username = models.CharField(max_length=200, verbose_name=_("用户名"))
    password = models.TextField(verbose_name=_("密码"))  # 加密存储

    # 系统连接信息
    system_type = models.CharField(
        max_length=20,
        choices=SYSTEM_TYPE_CHOICES,
        default="other",
        verbose_name=_("系统类型"),
    )
    ip_address = models.CharField(
        max_length=255, null=True, blank=True, verbose_name=_("IP地址")
    )
    port = models.PositiveIntegerField(null=True, blank=True, verbose_name=_("端口"))
    url = models.URLField(null=True, blank=True, verbose_name=_("访问地址"))

    # 操作系统类型
    os_type = models.CharField(
        max_length=20,
        choices=OS_TYPE_CHOICES,
        null=True,
        blank=True,
        verbose_name=_("操作系统类型"),
    )

    # 中间件类型
    mdw_type = models.CharField(
        max_length=20,
        choices=MDW_TYPE_CHOICES,
        null=True,
        blank=True,
        verbose_name=_("中间件类型"),
    )

    # 数据库特定信息
    database_type = models.CharField(
        max_length=20,
        choices=DATABASE_TYPE_CHOICES,
        null=True,
        blank=True,
        verbose_name=_("数据库类型"),
    )
    database_name = models.CharField(
        max_length=100, null=True, blank=True, verbose_name=_("数据库名")
    )

    # 环境和业务信息
    environment = models.CharField(
        max_length=20,
        choices=[
            ("dev", _("开发环境")),
            ("test", _("测试环境")),
            ("staging", _("预发布环境")),
            ("prod", _("生产环境")),
        ],
        blank=True,
        verbose_name=_("环境类型"),
    )
    project_name = models.CharField(max_length=100, verbose_name=_("应用/项目名称"))
    created_by = models.CharField(max_length=100, blank=True, verbose_name=_("创建人"))

    # 备注
    notes = models.TextField(blank=True, verbose_name=_("备注"))

    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("分类"),
    )

    owner = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="owned_passwords",
        verbose_name=_("所有者"),
    )

    # 组权限关系
    groups = models.ManyToManyField(
        PasswordEntryGroup,
        through="PasswordEntryGroupMembership",
        related_name="password_entries",
        blank=True,
        verbose_name=_("所属组"),
    )

    # 密码强度和安全信息
    strength = models.CharField(
        max_length=20,
        choices=STRENGTH_CHOICES,
        default="medium",
        verbose_name=_("密码强度"),
    )
    last_used = models.DateTimeField(
        null=True, blank=True, verbose_name=_("最后使用时间")
    )
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name=_("过期时间"))

    # 收藏和置顶
    is_favorite = models.BooleanField(default=False, verbose_name=_("收藏"))
    is_pinned = models.BooleanField(default=False, verbose_name=_("置顶"))

    # 软删除字段
    is_deleted = models.BooleanField(default=False, verbose_name=_("是否已删除"))
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name=_("删除时间"))
    deleted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="deleted_passwords",
        verbose_name=_("删除者"),
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("密码条目")
        verbose_name_plural = _("密码条目")
        db_table = "password_entries"
        ordering = ["-is_pinned", "-updated_at"]
        indexes = [
            # 权限查询优化索引
            models.Index(fields=["owner", "is_deleted"]),
            models.Index(fields=["is_deleted", "created_at"]),
            models.Index(fields=["is_deleted", "updated_at"]),
        ]

    def __str__(self):
        return self.title

    @property
    def is_expired(self):
        """检查密码是否过期"""
        if self.expires_at:
            from django.utils import timezone

            return timezone.now() > self.expires_at
        return False

    def soft_delete(self, user=None):
        """软删除密码条目"""
        from django.utils import timezone

        self.is_deleted = True
        self.deleted_at = timezone.now()
        if user:
            self.deleted_by = user
        self.save(update_fields=["is_deleted", "deleted_at", "deleted_by"])

    def restore(self):
        """恢复已删除的密码条目"""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        self.save(update_fields=["is_deleted", "deleted_at", "deleted_by"])

    def save(self, *args, **kwargs):
        """保存时评估密码强度（密码加密在序列化器中处理）"""
        # 如果密码字段有变化且未加密，则评估强度
        # 注意：密码加密现在在序列化器中处理，避免双重加密
        if (
            self.password
            and not getattr(
                self, "_password_encrypted", False
            )  # 检查是否已在序列化器中加密
            and len(self.password) < 100
        ):  # 简单检查：加密后的密码通常很长
            from utils.password_strength import analyze_password_strength

            # 在加密前评估密码强度
            strength_analysis = analyze_password_strength(self.password)
            self.strength = strength_analysis["strength"]

        super().save(*args, **kwargs)

    @property
    def connection_string(self):
        """生成连接字符串"""
        if self.ip_address:
            if self.port:
                return f"{self.ip_address}:{self.port}"
            return self.ip_address
        return ""

    @property
    def full_url(self):
        """生成完整的访问URL"""
        if self.url:
            return self.url
        elif self.ip_address:
            port_part = f":{self.port}" if self.port else ""
            return f"http://{self.ip_address}{port_part}"
        return ""

    @property
    def database_connection_string(self):
        """生成数据库连接字符串"""
        if self.system_type == "database" and self.database_type and self.ip_address:
            port_part = f":{self.port}" if self.port else ""
            db_part = f"/{self.database_name}" if self.database_name else ""
            return f"{self.database_type}://{self.ip_address}{port_part}{db_part}"
        return ""

    @property
    def ssh_command(self):
        """生成SSH连接命令"""
        if self.system_type == "os" and self.ip_address and self.username:
            port_part = f" -p {self.port}" if self.port and self.port != 22 else ""
            return f"ssh {self.username}@{self.ip_address}{port_part}"
        return ""

    @property
    def system_info(self):
        """获取系统信息摘要"""
        info = []
        if self.system_type:
            info.append(self.get_system_type_display())
        if self.environment:
            info.append(self.get_environment_display())
        if self.project_name:
            info.append(self.project_name)
        return " | ".join(info)

    def get_decrypted_password(self, master_key=None):
        """获取解密后的密码"""
        if not self.password:
            return ""
        try:
            from utils.encryption import decrypt_data

            return decrypt_data(self.password, master_key)
        except Exception:
            return "[解密失败]"


class CustomField(models.Model):
    """自定义字段模型"""

    FIELD_TYPES = [
        ("text", _("文本")),
        ("password", _("密码")),
        ("email", _("邮箱")),
        ("url", _("网址")),
        ("phone", _("电话")),
        ("date", _("日期")),
        ("number", _("数字")),
    ]

    password_entry = models.ForeignKey(
        PasswordEntry,
        on_delete=models.CASCADE,
        related_name="custom_fields",
        verbose_name=_("密码条目"),
    )
    field_name = models.CharField(max_length=100, verbose_name=_("字段名称"))
    field_type = models.CharField(
        max_length=20, choices=FIELD_TYPES, default="text", verbose_name=_("字段类型")
    )
    field_value = models.TextField(verbose_name=_("字段值"))  # 敏感数据加密存储
    is_sensitive = models.BooleanField(default=False, verbose_name=_("敏感数据"))
    order = models.IntegerField(default=0, verbose_name=_("排序"))

    class Meta:
        verbose_name = _("自定义字段")
        verbose_name_plural = _("自定义字段")
        db_table = "password_custom_fields"
        ordering = ["order", "id"]

    def __str__(self):
        return f"{self.password_entry.title} - {self.field_name}"


class Attachment(models.Model):
    """附件模型"""

    password_entry = models.ForeignKey(
        PasswordEntry,
        on_delete=models.CASCADE,
        related_name="attachments",
        verbose_name=_("密码条目"),
    )
    file_name = models.CharField(max_length=255, verbose_name=_("文件名"))
    file_path = models.CharField(max_length=500, verbose_name=_("文件路径"))  # 加密存储
    file_size = models.BigIntegerField(verbose_name=_("文件大小"))
    content_type = models.CharField(max_length=100, verbose_name=_("文件类型"))
    uploaded_by = models.ForeignKey(
        User, on_delete=models.CASCADE, verbose_name=_("上传用户")
    )
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name=_("上传时间"))

    class Meta:
        verbose_name = _("附件")
        verbose_name_plural = _("附件")
        db_table = "password_attachments"

    def __str__(self):
        return f"{self.password_entry.title} - {self.file_name}"


class PasswordHistory(models.Model):
    """密码历史记录模型"""

    password_entry = models.ForeignKey(
        PasswordEntry,
        on_delete=models.CASCADE,
        related_name="password_history",
        verbose_name=_("密码条目"),
    )
    old_password = models.TextField(verbose_name=_("旧密码"))  # 加密存储
    changed_by = models.ForeignKey(
        User, on_delete=models.CASCADE, verbose_name=_("修改用户")
    )
    changed_at = models.DateTimeField(auto_now_add=True, verbose_name=_("修改时间"))

    class Meta:
        verbose_name = _("密码历史")
        verbose_name_plural = _("密码历史")
        db_table = "password_history"
        ordering = ["-changed_at"]

    def __str__(self):
        return f"{self.password_entry.title} - {self.changed_at}"


class PasswordEntryGroupMembership(models.Model):
    """密码条目组成员关系中间表"""

    password_entry = models.ForeignKey(
        PasswordEntry, on_delete=models.CASCADE, verbose_name=_("密码条目")
    )
    group = models.ForeignKey(
        PasswordEntryGroup, on_delete=models.CASCADE, verbose_name=_("密码组")
    )
    added_by = models.ForeignKey(
        User, on_delete=models.CASCADE, verbose_name=_("添加者")
    )
    added_at = models.DateTimeField(auto_now_add=True, verbose_name=_("添加时间"))

    class Meta:
        verbose_name = _("密码条目组成员关系")
        verbose_name_plural = _("密码条目组成员关系")
        db_table = "password_entry_group_memberships"
        unique_together = ["password_entry", "group"]

    def __str__(self):
        return f"{self.password_entry.title} - {self.group.name}"


class PasswordPolicy(models.Model):
    """密码策略模型 - 定义密码生成和验证规则"""

    name = models.CharField(max_length=100, verbose_name=_("策略名称"))
    description = models.TextField(blank=True, verbose_name=_("策略描述"))

    # 密码长度要求
    min_length = models.PositiveIntegerField(
        default=8, verbose_name=_("密码最小长度"), help_text=_("密码的最小字符数")
    )
    max_length = models.PositiveIntegerField(
        default=128, verbose_name=_("密码最大长度"), help_text=_("密码的最大字符数")
    )

    # 字符类型要求
    uppercase_count = models.PositiveIntegerField(
        default=1,
        verbose_name=_("大写字母最少个数"),
        help_text=_("密码中必须包含的大写字母最少个数"),
    )
    lowercase_count = models.PositiveIntegerField(
        default=1,
        verbose_name=_("小写字母最少个数"),
        help_text=_("密码中必须包含的小写字母最少个数"),
    )
    digit_count = models.PositiveIntegerField(
        default=1,
        verbose_name=_("数字最少个数"),
        help_text=_("密码中必须包含的数字最少个数"),
    )
    special_char_count = models.PositiveIntegerField(
        default=1,
        verbose_name=_("特殊字符最少个数"),
        help_text=_("密码中必须包含的特殊字符最少个数"),
    )

    # 允许的特殊字符
    allowed_special_chars = models.CharField(
        max_length=100,
        default="!@#$%^&*()_+-=[]{}|;:,.<>?",
        verbose_name=_("允许的特殊字符"),
        help_text=_("密码中允许使用的特殊字符集合"),
    )

    # 密码规则
    allow_repeated_chars = models.BooleanField(
        default=False,
        verbose_name=_("是否允许重复字符"),
        help_text=_("是否允许密码中出现连续重复的字符"),
    )
    max_repeated_chars = models.PositiveIntegerField(
        default=2,
        verbose_name=_("最大重复字符数"),
        help_text=_("允许连续重复字符的最大个数"),
    )

    # 字典检查
    forbid_common_passwords = models.BooleanField(
        default=True,
        verbose_name=_("禁止常见密码"),
        help_text=_("是否禁止使用常见的弱密码"),
    )
    forbid_personal_info = models.BooleanField(
        default=True,
        verbose_name=_("禁止个人信息"),
        help_text=_("是否禁止密码中包含用户名、邮箱等个人信息"),
    )

    # 策略状态
    is_active = models.BooleanField(
        default=True, verbose_name=_("是否启用"), help_text=_("是否启用此密码策略")
    )
    is_default = models.BooleanField(
        default=False,
        verbose_name=_("是否为默认策略"),
        help_text=_("是否为系统默认的密码策略"),
    )

    # 创建信息
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="created_password_policies",
        verbose_name=_("创建者"),
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("创建时间"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("更新时间"))

    class Meta:
        verbose_name = _("密码策略")
        verbose_name_plural = _("密码策略")
        db_table = "password_policies"
        ordering = ["-is_default", "-created_at"]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # 确保只有一个默认策略
        if self.is_default:
            PasswordPolicy.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)

    def validate_password(self, password, user=None):
        """
        验证密码是否符合策略要求
        返回 (is_valid, errors) 元组
        """
        errors = []

        # 检查长度
        if len(password) < self.min_length:
            errors.append(f"密码长度不能少于{self.min_length}个字符")
        if len(password) > self.max_length:
            errors.append(f"密码长度不能超过{self.max_length}个字符")

        # 检查字符类型
        uppercase_count = sum(1 for c in password if c.isupper())
        if uppercase_count < self.uppercase_count:
            errors.append(f"密码至少需要{self.uppercase_count}个大写字母")

        lowercase_count = sum(1 for c in password if c.islower())
        if lowercase_count < self.lowercase_count:
            errors.append(f"密码至少需要{self.lowercase_count}个小写字母")

        digit_count = sum(1 for c in password if c.isdigit())
        if digit_count < self.digit_count:
            errors.append(f"密码至少需要{self.digit_count}个数字")

        special_count = sum(1 for c in password if c in self.allowed_special_chars)
        if special_count < self.special_char_count:
            errors.append(f"密码至少需要{self.special_char_count}个特殊字符")

        # 检查重复字符
        if not self.allow_repeated_chars:
            for i in range(len(password) - self.max_repeated_chars):
                if len(set(password[i : i + self.max_repeated_chars + 1])) == 1:
                    errors.append(
                        f"密码不能包含超过{self.max_repeated_chars}个连续重复字符"
                    )
                    break

        # 检查个人信息
        if self.forbid_personal_info and user:
            if user.username.lower() in password.lower():
                errors.append("密码不能包含用户名")
            if (
                hasattr(user, "email")
                and user.email
                and user.email.split("@")[0].lower() in password.lower()
            ):
                errors.append("密码不能包含邮箱前缀")

        return len(errors) == 0, errors

    def generate_password(self):
        """
        根据策略生成符合要求的密码
        """
        import random
        import string

        # 构建字符集
        chars = ""
        password_chars = []

        # 确保包含必需的字符类型
        if self.uppercase_count > 0:
            chars += string.ascii_uppercase
            password_chars.extend(
                random.choices(string.ascii_uppercase, k=self.uppercase_count)
            )

        if self.lowercase_count > 0:
            chars += string.ascii_lowercase
            password_chars.extend(
                random.choices(string.ascii_lowercase, k=self.lowercase_count)
            )

        if self.digit_count > 0:
            chars += string.digits
            password_chars.extend(random.choices(string.digits, k=self.digit_count))

        if self.special_char_count > 0:
            chars += self.allowed_special_chars
            password_chars.extend(
                random.choices(self.allowed_special_chars, k=self.special_char_count)
            )

        # 填充到最小长度
        remaining_length = self.min_length - len(password_chars)
        if remaining_length > 0:
            password_chars.extend(random.choices(chars, k=remaining_length))

        # 打乱顺序
        random.shuffle(password_chars)

        password = "".join(password_chars)

        # 验证生成的密码
        is_valid, _ = self.validate_password(password)
        if not is_valid:
            # 如果生成的密码不符合要求，递归重新生成
            return self.generate_password()

        return password


class PasswordPermission(models.Model):
    """密码权限模型 - 管理用户和用户组对密码的访问权限"""

    PERMISSION_TYPE_CHOICES = [
        ("user", _("用户")),
        ("group", _("用户组")),
    ]

    PERMISSION_LEVEL_CHOICES = [
        ("browse", _("浏览权限")),  # 只能看到基本信息，看不到密码
        ("read", _("读取权限")),  # 可以查看所有信息包括密码
        ("write", _("编辑权限")),  # 可以修改密码条目信息
    ]

    password = models.ForeignKey(
        PasswordEntry,
        on_delete=models.CASCADE,
        related_name="permissions",
        verbose_name=_("密码条目"),
    )
    permission_type = models.CharField(
        max_length=10, choices=PERMISSION_TYPE_CHOICES, verbose_name=_("权限类型")
    )
    target_id = models.PositiveIntegerField(
        verbose_name=_("目标ID"), help_text=_("用户ID或用户组ID")
    )
    permission_level = models.CharField(
        max_length=10, choices=PERMISSION_LEVEL_CHOICES, verbose_name=_("权限级别")
    )
    granted_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="granted_password_permissions",
        verbose_name=_("授权人"),
    )
    granted_at = models.DateTimeField(auto_now_add=True, verbose_name=_("授权时间"))
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("过期时间"),
        help_text=_("权限过期时间，为空表示永不过期"),
    )
    is_active = models.BooleanField(default=True, verbose_name=_("是否激活"))

    class Meta:
        verbose_name = _("密码权限")
        verbose_name_plural = _("密码权限")
        db_table = "password_permissions"
        unique_together = ["password", "permission_type", "target_id"]
        indexes = [
            models.Index(fields=["password", "permission_type", "target_id"]),
            models.Index(fields=["permission_type", "target_id"]),
            models.Index(fields=["granted_by"]),
            # 权限过滤查询优化索引
            models.Index(fields=["permission_type", "target_id", "is_active"]),
            models.Index(fields=["password", "is_active", "expires_at"]),
            models.Index(fields=["is_active", "expires_at"]),
        ]

    def __str__(self):
        target_type = "用户" if self.permission_type == "user" else "用户组"
        return f"{self.password.title} - {target_type}({self.target_id}) - {self.get_permission_level_display()}"

    @property
    def is_expired(self):
        """检查权限是否过期"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def get_target_object(self):
        """获取权限目标对象（用户或用户组）"""
        if self.permission_type == "user":
            try:
                return User.objects.get(id=self.target_id)
            except User.DoesNotExist:
                return None
        elif self.permission_type == "group":
            try:
                return PasswordEntryGroup.objects.get(id=self.target_id)
            except PasswordEntryGroup.DoesNotExist:
                return None
        return None


class UserGroupMembership(models.Model):
    """用户组成员关系模型 - 管理用户在密码组中的成员关系"""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="group_memberships",
        verbose_name=_("用户"),
    )
    group = models.ForeignKey(
        PasswordEntryGroup,
        on_delete=models.CASCADE,
        related_name="user_memberships",
        verbose_name=_("密码组"),
    )
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name=_("加入时间"))
    added_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="added_group_memberships",
        verbose_name=_("添加者"),
    )
    is_active = models.BooleanField(default=True, verbose_name=_("是否激活"))

    class Meta:
        verbose_name = _("用户组成员")
        verbose_name_plural = _("用户组成员")
        db_table = "user_group_memberships"
        unique_together = ["user", "group"]
        indexes = [
            models.Index(fields=["user", "group"]),
            models.Index(fields=["group", "is_active"]),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.group.name}"
