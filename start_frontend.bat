@echo off
echo ========================================
echo 启动密码管理系统前端服务
echo ========================================

cd /d "%~dp0frontend"

echo 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Node.js 未安装，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo 检查依赖...
if not exist "node_modules" (
    echo 安装依赖...
    npm install
)

echo 启动前端开发服务器...
echo 服务地址: http://localhost:5668
echo 按 Ctrl+C 停止服务
echo ========================================

npm run dev:antd
