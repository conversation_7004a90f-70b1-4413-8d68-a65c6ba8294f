# Generated by Django 5.2.4 on 2025-08-01 17:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("passwords", "0015_remove_passwordentry_description_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="PasswordPermission",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "permission_type",
                    models.CharField(
                        choices=[("user", "用户"), ("group", "用户组")],
                        max_length=10,
                        verbose_name="权限类型",
                    ),
                ),
                (
                    "target_id",
                    models.PositiveIntegerField(
                        help_text="用户ID或用户组ID", verbose_name="目标ID"
                    ),
                ),
                (
                    "permission_level",
                    models.CharField(
                        choices=[
                            ("browse", "浏览权限"),
                            ("read", "读取权限"),
                            ("write", "编辑权限"),
                        ],
                        max_length=10,
                        verbose_name="权限级别",
                    ),
                ),
                (
                    "granted_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="授权时间"),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="权限过期时间，为空表示永不过期",
                        null=True,
                        verbose_name="过期时间",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "granted_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="granted_password_permissions",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="授权人",
                    ),
                ),
                (
                    "password",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permissions",
                        to="passwords.passwordentry",
                        verbose_name="密码条目",
                    ),
                ),
            ],
            options={
                "verbose_name": "密码权限",
                "verbose_name_plural": "密码权限",
                "db_table": "password_permissions",
                "indexes": [
                    models.Index(
                        fields=["password", "permission_type", "target_id"],
                        name="password_pe_passwor_e7d8b9_idx",
                    ),
                    models.Index(
                        fields=["permission_type", "target_id"],
                        name="password_pe_permiss_425c36_idx",
                    ),
                    models.Index(
                        fields=["granted_by"], name="password_pe_granted_64ed75_idx"
                    ),
                ],
                "unique_together": {("password", "permission_type", "target_id")},
            },
        ),
        migrations.CreateModel(
            name="UserGroupMembership",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "joined_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="加入时间"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "added_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="added_group_memberships",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="添加者",
                    ),
                ),
                (
                    "group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_memberships",
                        to="passwords.passwordentrygroup",
                        verbose_name="密码组",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="group_memberships",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "用户组成员",
                "verbose_name_plural": "用户组成员",
                "db_table": "user_group_memberships",
                "indexes": [
                    models.Index(
                        fields=["user", "group"], name="user_group__user_id_84db00_idx"
                    ),
                    models.Index(
                        fields=["group", "is_active"],
                        name="user_group__group_i_b2f28e_idx",
                    ),
                ],
                "unique_together": {("user", "group")},
            },
        ),
    ]
