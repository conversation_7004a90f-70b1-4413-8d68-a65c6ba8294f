"""
密码权限管理系统的Playwright自动化测试
验证各项权限控制和功能是否符合预期
"""

import asyncio
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
import time

# 测试配置
FRONTEND_URL = "http://localhost:5668"
BACKEND_URL = "http://localhost:8001"

# 测试用户账号
TEST_USERS = {
    "admin": {"username": "admin", "password": "admin123"},
    "user1": {"username": "user1", "password": "password123"},
    "user2": {"username": "user2", "password": "password123"},
    "user3": {"username": "user3", "password": "password123"},
    "viewer1": {"username": "viewer1", "password": "password123"},
}


class PermissionTester:
    def __init__(self):
        self.browser = None
        self.contexts = {}
        self.pages = {}

    async def setup(self):
        """初始化浏览器和页面"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=False, slow_mo=1000)

        # 为每个测试用户创建独立的浏览器上下文
        for user_key in TEST_USERS.keys():
            self.contexts[user_key] = await self.browser.new_context()
            self.pages[user_key] = await self.contexts[user_key].new_page()

    async def login_user(self, user_key: str):
        """登录指定用户"""
        page = self.pages[user_key]
        user_info = TEST_USERS[user_key]

        print(f"🔐 登录用户: {user_info['username']}")

        # 访问登录页面
        await page.goto(FRONTEND_URL)
        await page.wait_for_load_state("networkidle")

        # 检查是否已经登录
        try:
            await page.wait_for_selector("text=密码列表", timeout=3000)
            print(f"✅ 用户 {user_info['username']} 已经登录")
            return True
        except:
            pass

        # 执行登录
        try:
            # 等待登录表单加载
            await page.wait_for_selector(
                'textbox[placeholder="请输入用户名"]', timeout=10000
            )

            # 填写登录表单
            await page.get_by_placeholder("请输入用户名").fill(user_info["username"])
            await page.get_by_placeholder("密码").fill(user_info["password"])

            # 点击登录按钮
            await page.get_by_role("button", name="login").click()

            # 等待登录成功，检查是否跳转到主页
            await page.wait_for_url("**/passwords", timeout=15000)
            print(f"✅ 用户 {user_info['username']} 登录成功")
            return True

        except Exception as e:
            print(f"❌ 用户 {user_info['username']} 登录失败: {e}")
            return False

    async def test_password_list_permissions(self):
        """测试密码列表页面的权限控制"""
        print("\n📋 测试密码列表页面权限控制")

        for user_key in ["admin", "user1", "user2", "viewer1"]:
            page = self.pages[user_key]
            user_info = TEST_USERS[user_key]

            print(f"\n👤 测试用户: {user_info['username']}")

            # 访问密码列表页面
            await page.goto(f"{FRONTEND_URL}/passwords")
            await page.wait_for_load_state("networkidle")

            # 检查页面是否正常加载
            try:
                await page.wait_for_selector("text=密码列表", timeout=5000)
                print(f"✅ {user_info['username']} 可以访问密码列表页面")

                # 检查密码条目数量
                password_rows = await page.query_selector_all("tbody tr")
                print(
                    f"📊 {user_info['username']} 可以看到 {len(password_rows)} 个密码条目"
                )

                # 检查操作按钮的可见性
                for i, row in enumerate(password_rows[:3]):  # 只检查前3个
                    # 检查查看按钮
                    view_btn = await row.query_selector('button[title="查看详情"]')
                    edit_btn = await row.query_selector('button[title="编辑"]')
                    delete_btn = await row.query_selector(
                        'button[title="移动到回收站"]'
                    )

                    print(
                        f"  条目 {i+1}: 查看={view_btn is not None}, 编辑={edit_btn is not None}, 删除={delete_btn is not None}"
                    )

            except Exception as e:
                print(f"❌ {user_info['username']} 访问密码列表失败: {e}")

    async def test_password_detail_permissions(self):
        """测试密码详情页面的权限控制"""
        print("\n🔍 测试密码详情页面权限控制")

        # 测试不同用户访问MySQL生产数据库的权限
        test_cases = [
            ("admin", "admin", "MySQL生产数据库"),  # owner，应该有完全权限
            ("user1", "user1", "MySQL生产数据库"),  # 有read权限
            ("user2", "user2", "MySQL生产数据库"),  # 有browse权限
            ("viewer1", "viewer1", "MySQL生产数据库"),  # 无权限
        ]

        for user_key, username, password_title in test_cases:
            page = self.pages[user_key]

            print(f"\n👤 测试用户 {username} 访问 {password_title}")

            # 访问密码列表页面
            await page.goto(f"{FRONTEND_URL}/passwords")
            await page.wait_for_load_state("networkidle")

            try:
                # 查找目标密码条目
                password_link = await page.query_selector(f"text={password_title}")
                if password_link:
                    await password_link.click()
                    await page.wait_for_load_state("networkidle")

                    # 检查页面标题
                    title = await page.query_selector("h1")
                    if title:
                        title_text = await title.text_content()
                        print(f"✅ {username} 可以访问详情页面: {title_text}")

                        # 检查密码字段是否可见
                        password_field = await page.query_selector(
                            'input[type="password"]'
                        )
                        if password_field:
                            # 检查是否可以查看密码
                            show_btn = await page.query_selector(
                                'button[title*="显示密码"]'
                            )
                            if show_btn:
                                await show_btn.click()
                                await page.wait_for_timeout(1000)

                                password_value = await password_field.get_attribute(
                                    "value"
                                )
                                if password_value and password_value != "••••••••••••":
                                    print(f"✅ {username} 可以查看密码内容")
                                else:
                                    print(f"⚠️ {username} 无法查看密码内容")
                            else:
                                print(f"⚠️ {username} 没有显示密码按钮")

                        # 检查编辑按钮
                        edit_btn = await page.query_selector('button:has-text("编辑")')
                        if edit_btn:
                            is_disabled = await edit_btn.is_disabled()
                            print(
                                f"📝 {username} 编辑按钮: {'禁用' if is_disabled else '可用'}"
                            )
                        else:
                            print(f"📝 {username} 没有编辑按钮")

                        # 检查删除按钮
                        delete_btn = await page.query_selector(
                            'button:has-text("删除")'
                        )
                        if delete_btn:
                            is_disabled = await delete_btn.is_disabled()
                            print(
                                f"🗑️ {username} 删除按钮: {'禁用' if is_disabled else '可用'}"
                            )
                        else:
                            print(f"🗑️ {username} 没有删除按钮")

                    else:
                        print(f"❌ {username} 无法获取页面标题")
                else:
                    print(f"❌ {username} 找不到密码条目: {password_title}")

            except Exception as e:
                print(f"❌ {username} 访问详情页面失败: {e}")

    async def test_permission_management(self):
        """测试权限管理功能"""
        print("\n👥 测试权限管理功能")

        # 只有admin和密码owner可以管理权限
        test_cases = [
            ("admin", "MySQL生产数据库"),  # admin是owner
            ("user1", "Redis缓存服务器"),  # user1是owner
        ]

        for user_key, password_title in test_cases:
            page = self.pages[user_key]
            username = TEST_USERS[user_key]["username"]

            print(f"\n👤 测试用户 {username} 管理 {password_title} 的权限")

            try:
                # 访问密码列表页面
                await page.goto(f"{FRONTEND_URL}/passwords")
                await page.wait_for_load_state("networkidle")

                # 查找目标密码条目并点击
                password_link = await page.query_selector(f"text={password_title}")
                if password_link:
                    await password_link.click()
                    await page.wait_for_load_state("networkidle")

                    # 查找权限管理按钮
                    permission_btn = await page.query_selector(
                        'button:has-text("权限管理")'
                    )
                    if permission_btn:
                        await permission_btn.click()
                        await page.wait_for_load_state("networkidle")

                        # 检查权限管理页面是否加载
                        page_title = await page.query_selector(
                            'h1:has-text("权限管理")'
                        )
                        if page_title:
                            print(f"✅ {username} 可以访问权限管理页面")

                            # 检查授予权限按钮
                            grant_btn = await page.query_selector(
                                'button:has-text("授予权限")'
                            )
                            if grant_btn:
                                print(f"✅ {username} 可以看到授予权限按钮")

                                # 测试授予权限功能
                                await grant_btn.click()
                                await page.wait_for_timeout(1000)

                                # 检查模态框是否打开
                                modal = await page.query_selector(".ant-modal")
                                if modal:
                                    print(f"✅ {username} 可以打开授予权限模态框")

                                    # 关闭模态框
                                    cancel_btn = await page.query_selector(
                                        '.ant-modal button:has-text("取消")'
                                    )
                                    if cancel_btn:
                                        await cancel_btn.click()
                                        await page.wait_for_timeout(500)
                                else:
                                    print(f"❌ {username} 无法打开授予权限模态框")
                            else:
                                print(f"❌ {username} 看不到授予权限按钮")
                        else:
                            print(f"❌ {username} 无法访问权限管理页面")
                    else:
                        print(f"❌ {username} 看不到权限管理按钮")
                else:
                    print(f"❌ {username} 找不到密码条目: {password_title}")

            except Exception as e:
                print(f"❌ {username} 权限管理测试失败: {e}")

    async def test_api_permissions(self):
        """测试API权限控制"""
        print("\n🔌 测试API权限控制")

        # 测试不同用户访问API的权限
        for user_key in ["admin", "user1", "user2", "viewer1"]:
            page = self.pages[user_key]
            username = TEST_USERS[user_key]["username"]

            print(f"\n👤 测试用户 {username} 的API权限")

            try:
                # 通过浏览器发送API请求测试权限
                response = await page.evaluate(
                    f"""
                    fetch('{BACKEND_URL}/api/passwords/', {{
                        method: 'GET',
                        credentials: 'include',
                        headers: {{
                            'Content-Type': 'application/json',
                        }}
                    }})
                    .then(response => {{
                        return {{
                            status: response.status,
                            ok: response.ok
                        }};
                    }})
                    .catch(error => {{
                        return {{
                            status: 0,
                            ok: false,
                            error: error.message
                        }};
                    }});
                """
                )

                if response["ok"]:
                    print(f"✅ {username} 可以访问密码列表API")
                else:
                    print(
                        f"❌ {username} 无法访问密码列表API (状态码: {response['status']})"
                    )

            except Exception as e:
                print(f"❌ {username} API测试失败: {e}")

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始密码权限管理系统测试")
        print("=" * 50)

        try:
            # 初始化
            await self.setup()

            # 登录所有测试用户
            print("\n🔐 登录测试用户")
            for user_key in TEST_USERS.keys():
                success = await self.login_user(user_key)
                if not success:
                    print(f"⚠️ 用户 {user_key} 登录失败，跳过相关测试")

            # 等待一下确保登录完成
            await asyncio.sleep(2)

            # 运行各项测试
            await self.test_password_list_permissions()
            await self.test_password_detail_permissions()
            await self.test_permission_management()
            await self.test_api_permissions()

            print("\n" + "=" * 50)
            print("✅ 所有测试完成")

        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")

        finally:
            # 清理资源
            if self.browser:
                await self.browser.close()


async def main():
    """主函数"""
    tester = PermissionTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
