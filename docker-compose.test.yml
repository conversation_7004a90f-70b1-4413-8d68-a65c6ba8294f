version: '3.8'

# 简化测试配置 - 验证基础镜像功能

services:
  # 数据库服务
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: testdb
      MYSQL_USER: testuser
      MYSQL_PASSWORD: testpass
      MYSQL_ROOT_PASSWORD: rootpass
    ports:
      - "3307:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis服务
  redis:
    image: redis:7.4
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # 后端测试 - 验证依赖安装
  backend-test:
    image: locker-template-backend-base:v3
    entrypoint: ""
    command: >
      sh -c "
        echo '测试开始: 验证Python依赖安装' &&
        python -c 'import django; print(f\"Django版本: {django.get_version()}\")' &&
        python -c 'import rest_framework; print(\"DRF已安装\")' &&
        python -c 'import redis; print(\"Redis客户端已安装\")' &&
        python -c 'import MySQLdb; print(\"MySQL客户端已安装\")' &&
        python -c 'import requests; print(\"Requests已安装\")' &&
        echo '✅ 所有核心依赖验证成功!' &&
        echo '🚀 基础镜像功能正常' &&
        sleep 30
      "
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  # 前端测试 - 验证依赖安装
  frontend-test:
    image: locker-template-frontend-base:latest
    entrypoint: ""
    command: >
      sh -c "
        echo '测试开始: 验证Node.js依赖安装' &&
        node --version &&
        npm --version &&
        npm list vue --depth=0 &&
        npm list axios --depth=0 &&
        echo '✅ 所有核心依赖验证成功!' &&
        echo '🚀 前端基础镜像功能正常' &&
        sleep 30
      "
    ports:
      - "3000:3000"
